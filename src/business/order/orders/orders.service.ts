/* eslint-disable @typescript-eslint/no-unused-vars */
import { Injectable } from '@nestjs/common';
import { OrderRepository } from './infrastructure/repositories/order.repository';
import { OrderItemRepository } from './infrastructure/repositories/order-item.repository';
import { OrderStatusHistoryRepository } from './infrastructure/repositories/order-status-history.repository';
import { OrderAssignmentHistoryRepository } from './infrastructure/repositories/order-assignment-history.repository';
import { OrderStopHistoryRepository } from './infrastructure/repositories/order-stop-history.repository';
import { TrackingNumberService } from './services/tracking-number.service';
import { OrderStatusService } from './services/order-status.service';
import { OrderAssignmentService } from './services/order-assignment.service';
import { AddressService } from '@app/business/address/addresses/address.service';
import { calculateAddressDistanceById } from '@utils/distance.utils';
import { PriceModifiersService } from '@app/business/pricing/price-modifiers/price-modifiers.service';
import { OrderDetailsDto } from '@app/business/pricing/price-modifiers/dto/calculate-price.dto';
import { CreateOrderDto } from './dto/create-order.dto';
import { EnhancedOrderDto } from './dto/enhanced-order.dto';
import { UpdateOrderDto } from './dto/update-order.dto';
import { OrderAttachments, OrderResponseDto } from './dto/order-response.dto';
import { OrderDetailResponseDto } from './dto/order-detail-response.dto';
import { CreateOrderItemDto } from './dto/create-order-item.dto';
import { UpdateOrderItemDto } from './dto/update-order-item.dto';
import {
  OrderStatus,
  OrderStopType,
  BillingStatus,
  PaymentStatus,
  DistanceUnit,
} from './domain/order.types';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { DataSource } from 'typeorm';
import { BaseFilterDto } from '@core/infrastructure/filtering/dtos/base-filter.dto';
import { PaginatedResult } from '@utils/query-creator/interfaces';
import { Order } from './domain/order';
import { OrderItem } from './domain/order-item';
import {
  OrderNotFoundException,
  OrderTenantMismatchException,
  OrderLockedException,
  InvalidOrderStatusTransitionException,
  OrderUpdateFailedException,
  OrderCreationFailedException,
  InvalidOrderDataException,
  OrderItemNotFoundException,
  OrderDeliveryTimeInvalidException,
  CustomModifierAlreadyExistsException,
  OrderUpdateBadRequestException,
} from '@utils/errors/exceptions/order.exceptions';
import { DraftOrderResponseDto } from '@app/business/order/orders/dto/draft-order-response.dto';
import { DraftOrderDto } from '@app/business/order/orders/dto/draft-order.dto';
import { NullableType } from '../../../utils/types/nullable.type';
import { PriceSetsService } from '../../pricing/price-sets/price-sets.service';
import { ZoneRepository } from '../../zone/zones/infrastructure/repositories/zone.repository';
import { FileUploadService } from '../../../core/file-upload/services/file-upload.service';
import { HistoryService } from '../../history/history.service';
import { HISTORY_ENTITIES } from '../../history/infrastructure/constants/entity.constants';
import { getHistoryPropertyType } from '../../../utils/general-utils';
import { VehiclesService } from '../../vehicle/vehicles/vehicles.service';
import { ConfigurationType } from '../../pricing/price-sets/domain/price-set.types';
import { FILE_UPLOAD_CONSTANTS } from '@app/core/file-upload/constants/file-uploads.constant';
import { OrderPricePreviewDto } from './dto/order-price-preview.dto';
import { ZoneTableRepository } from '../../zone/zone-tables/infrastructure/repositories/zone-table.repository';

@Injectable()
export class OrdersService {
  private readonly MAX_MINUTES = 5;

  constructor(
    private readonly orderRepository: OrderRepository,
    private readonly orderItemRepository: OrderItemRepository,
    private readonly orderStatusHistoryRepository: OrderStatusHistoryRepository,
    private readonly orderAssignmentHistoryRepository: OrderAssignmentHistoryRepository,
    private readonly orderStopHistoryRepository: OrderStopHistoryRepository,
    private readonly zoneRepository: ZoneRepository,
    private readonly trackingNumberService: TrackingNumberService,
    private readonly orderStatusService: OrderStatusService,
    private readonly orderAssignmentService: OrderAssignmentService,
    private readonly addressService: AddressService,
    private readonly connection: DataSource,
    private readonly eventEmitter: EventEmitter2,
    private readonly priceModifiersService: PriceModifiersService,
    // private readonly pricingService: PricingService
    private readonly fileUploadService: FileUploadService,
    private readonly historyService: HistoryService,
    private readonly vehiclesService: VehiclesService,
    private readonly priceSetService: PriceSetsService,
    private readonly zoneTableRepository: ZoneTableRepository,
  ) {}

  async createOrder(
    tenantId: string,
    userId: string,
    createOrderDto: EnhancedOrderDto,
  ): Promise<OrderResponseDto> {
    console.log('DEBUG - Order foreign key references:');
    console.log(`Tenant ID: ${tenantId}`);
    console.log(`Customer ID: ${createOrderDto.customerId}`);
    console.log(`Requested By ID: ${createOrderDto.requestedById}`);
    console.log(`Submitted By ID: ${createOrderDto.submittedById}`);
    console.log(
      `Package Template ID: No longer used - supporting multiple packages`,
    );

    const collectionAddressId = createOrderDto.collectionAddressId;
    console.log(`Collection Address ID: ${collectionAddressId}`);

    const deliveryAddressId = createOrderDto.deliveryAddressId;
    console.log(`Delivery Address ID: ${deliveryAddressId}`);
    const testId = createOrderDto?.orderId;

    const isPlacingDraft = !!testId;
    let existingOrder: Order | null = null;

    if (isPlacingDraft) {
      if (!createOrderDto.orderId) {
        throw new OrderCreationFailedException(
          'Draft order ID is required to place a draft.',
        );
      }
      existingOrder = await this.orderRepository.findById(testId);
      if (!existingOrder || existingOrder.status !== OrderStatus.Draft) {
        throw new OrderCreationFailedException(
          'Only existing draft orders can be placed.',
        );
      }

      // ✅ Add: Verify the draft belongs to the same tenant
      if (existingOrder.tenantId !== tenantId) {
        throw new OrderCreationFailedException(
          'Draft order does not belong to the specified tenant.',
        );
      }
    }
    let totalItems = 1;
    let totalWeight = 0;
    let totalVolume = 0;

    if (createOrderDto.items && createOrderDto.items.length > 0) {
      totalItems = createOrderDto.items.reduce(
        (sum, item) => sum + (item.quantity || 1),
        0,
      );

      totalWeight = createOrderDto.items.reduce(
        (sum, item) => sum + (item.totalWeight || 0) * (item.quantity || 1),
        0,
      );

      totalVolume = createOrderDto.items.reduce((max, item) => {
        if (item.length && item.width && item.height) {
          const volume = item.length * item.width * item.height;
          return Math.max(max, volume);
        }
        return max;
      }, 0);
    }

    console.log(`Calculated Total Items: ${totalItems}`);
    console.log(`Calculated Total Weight: ${totalWeight}`);
    console.log(`Calculated Total Volume: ${totalVolume}`);

    console.log(`Vehicle Type ID: No longer provided by frontend`);
    console.log(`Assigned Driver ID: No longer provided by frontend`);
    console.log(`Assigned Vehicle ID: No longer provided by frontend`);
    console.log(`Price Set ID: ${createOrderDto.priceSetId || 'NULL'}`);

    const queryRunner = this.connection.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      if (createOrderDto.customerId) {
        const customerExists = await queryRunner.manager.query(
          'SELECT EXISTS(SELECT 1 FROM users WHERE id = $1)',
          [createOrderDto.customerId],
        );
        if (!customerExists[0].exists) {
          throw new OrderCreationFailedException(
            `Customer with ID ${createOrderDto.customerId} does not exist`,
          );
        }
      }

      if (createOrderDto.requestedById) {
        const contactExists = await queryRunner.manager.query(
          'SELECT EXISTS(SELECT 1 FROM contacts WHERE id = $1)',
          [createOrderDto.requestedById],
        );
        if (!contactExists[0].exists) {
          throw new OrderCreationFailedException(
            `Contact (requested_by) with ID ${createOrderDto.requestedById} does not exist`,
          );
        }
      }

      if (createOrderDto.submittedById) {
        const contactExists = await queryRunner.manager.query(
          'SELECT EXISTS(SELECT 1 FROM contacts WHERE id = $1)',
          [createOrderDto.submittedById],
        );
        if (!contactExists[0].exists) {
          throw new OrderCreationFailedException(
            `Contact (submitted_by) with ID ${createOrderDto.submittedById} does not exist`,
          );
        }
      }

      if (createOrderDto.collectionAddressId) {
        const addressExists = await queryRunner.manager.query(
          'SELECT EXISTS(SELECT 1 FROM addresses WHERE id = $1)',
          [createOrderDto.collectionAddressId],
        );
        if (!addressExists[0].exists) {
          throw new OrderCreationFailedException(
            `Collection address with ID ${createOrderDto.collectionAddressId} does not exist`,
          );
        }
      }

      if (createOrderDto.collectionZoneId) {
        const zoneExists = await queryRunner.manager.query(
          'SELECT EXISTS(SELECT 1 FROM zones WHERE id = $1)',
          [createOrderDto.collectionZoneId],
        );
        if (!zoneExists[0].exists) {
          throw new OrderCreationFailedException(
            `Collection zone with ID ${createOrderDto.collectionZoneId} does not exist`,
          );
        }
      }

      if (createOrderDto.deliveryAddressId) {
        const addressExists = await queryRunner.manager.query(
          'SELECT EXISTS(SELECT 1 FROM addresses WHERE id = $1)',
          [createOrderDto.deliveryAddressId],
        );
        if (!addressExists[0].exists) {
          throw new OrderCreationFailedException(
            `Delivery address with ID ${createOrderDto.deliveryAddressId} does not exist`,
          );
        }
      }

      if (createOrderDto.deliveryZoneId) {
        const zoneExists = await queryRunner.manager.query(
          'SELECT EXISTS(SELECT 1 FROM zones WHERE id = $1)',
          [createOrderDto.deliveryZoneId],
        );
        if (!zoneExists[0].exists) {
          throw new OrderCreationFailedException(
            `Delivery zone with ID ${createOrderDto.deliveryZoneId} does not exist`,
          );
        }
      }

      if (createOrderDto.priceSetId) {
        const priceSetExists = await queryRunner.manager.query(
          'SELECT EXISTS(SELECT 1 FROM price_sets WHERE id = $1)',
          [createOrderDto.priceSetId],
        );
        if (!priceSetExists[0].exists) {
          throw new OrderCreationFailedException(
            `Price set with ID ${createOrderDto.priceSetId} does not exist`,
          );
        }
      }

      const trackingNumber = isPlacingDraft
        ? existingOrder!.trackingNumber
        : await this.trackingNumberService.generateTrackingNumber();

      const order = isPlacingDraft ? existingOrder! : new Order();
      order.tenantId = tenantId;
      order.trackingNumber = trackingNumber;
      order.referenceNumber = createOrderDto.referenceNumber;
      order.customerId = createOrderDto.customerId;
      order.requestedById = createOrderDto.requestedById;
      order.submittedById = createOrderDto.submittedById;

      if (!collectionAddressId) {
        throw new OrderCreationFailedException(
          'Collection address ID is required.',
        );
      }
      order.collectionAddressId = collectionAddressId;
      order.collectionContactName = createOrderDto.collectionContactName;
      order.collectionInstructions = createOrderDto.collectionInstructions;
      order.collectionSignatureRequired =
        createOrderDto.collectionSignatureRequired ?? false;
      order.scheduledCollectionTime = createOrderDto.scheduledCollectionTime;
      order.collectionZoneId = undefined;

      if (!deliveryAddressId) {
        throw new OrderCreationFailedException(
          'Delivery address ID is required.',
        );
      }
      order.deliveryAddressId = deliveryAddressId;
      order.deliveryContactName = createOrderDto.deliveryContactName;
      order.deliveryInstructions = createOrderDto.deliveryInstructions;
      order.deliverySignatureRequired =
        createOrderDto.deliverySignatureRequired ?? false;
      order.scheduledDeliveryTime = (
        createOrderDto as any
      ).scheduledDeliveryTime;

      order.deliveryZoneId = undefined;

      order.totalItems = totalItems;
      order.totalWeight = totalWeight;
      order.totalVolume = totalVolume;
      order.declaredValue = createOrderDto.declaredValue;
      order.codAmount = createOrderDto.codAmount;
      order.codCollected = false;
      order.isCod = createOrderDto.isCod || false;
      order.priceSetId = createOrderDto.priceSetId;
      order.optionsPrice = 0;
      order.miscAdjustment = 0;
      order.customerAdjustment = 0;
      order.billingStatus = BillingStatus.NotBilled;
      order.paymentStatus = PaymentStatus.Pending;

      const collectionAddress =
        await this.addressService.getAddressDetails(collectionAddressId);

      const deliveryAddress =
        await this.addressService.getAddressDetails(deliveryAddressId);

      if (collectionAddressId && deliveryAddressId) {
        try {
          console.log(
            'Calculating distance between addresses using Google Maps API...',
          );

          const calculatedDistance = await calculateAddressDistanceById(
            this.addressService,
            collectionAddressId,
            deliveryAddressId,
            DistanceUnit.Kilometers,
            collectionAddress?.latitude,
            collectionAddress?.longitude,
            deliveryAddress?.latitude,
            deliveryAddress?.longitude,
          );
          console.log(
            'Distance calculation result in create order:',
            calculatedDistance,
          );

          if (calculatedDistance > 0) {
            console.log(
              `Calculated distance: ${calculatedDistance} ${DistanceUnit.Kilometers}`,
            );
            order.distance = calculatedDistance;
          } else {
            console.log(
              'Could not calculate distance between addresses (no route found or missing coordinates)',
            );
          }
        } catch (error) {
          console.error('Error calculating distance between addresses:', error);
        }
      } else {
      }

      order.description = createOrderDto.description;
      order.comments = createOrderDto.comments;
      order.internalNotes = createOrderDto.internalNotes;
      order.customFields = createOrderDto.customFields || {};
      order.metadata = createOrderDto.metadata || {};
      order.isLocked = false;
      order.isDeleted = false;
      order.createdBy = userId;
      if (!isPlacingDraft) {
        order.submittedAt = new Date(); // Set only for new placed orders
      }

      console.log(
        'DEBUG - About to create order with the following data:',
        JSON.stringify(order, null, 2),
      );
      let basePrice = 0;

      if (
        createOrderDto.collectionAddressId &&
        createOrderDto.deliveryAddressId &&
        createOrderDto.priceSetId
      ) {
        try {
          const collectionPostalCode = collectionAddress.postalCode;
          const deliveryPostalCode = deliveryAddress.postalCode;

          const collectionZonePrefix = collectionPostalCode
            ?.trim()
            .substring(0, 3)
            .toUpperCase();
          const deliveryZonePrefix = deliveryPostalCode
            ?.trim()
            .substring(0, 3)
            .toUpperCase();

          const originZone =
            await this.zoneRepository.findLatestUpdatedZoneByPostalCode(
              collectionZonePrefix,
              tenantId,
            );
          const destinationZone =
            await this.zoneRepository.findLatestUpdatedZoneByPostalCode(
              deliveryZonePrefix,
              tenantId,
            );
          if (originZone && destinationZone) {
            order.collectionZoneId = originZone.id;
            order.deliveryZoneId = destinationZone.id;

            const zoneTable = await this.priceSetService.getBasePriceByZone(
              createOrderDto.priceSetId,
            );

            const matchedZone = zoneTable.zoneTableValues?.find((zone) => {
              return (
                zone.originZoneId === originZone.id &&
                zone.destinationZoneId === destinationZone.id
              );
            });

            if (matchedZone && matchedZone.value != null) {
              basePrice = matchedZone.value;
            } else {
              console.log('No matching zone pair found in price set.');
            }
          } else {
            console.log('Could not resolve zones for the given postal codes.');
          }
        } catch (error) {
          console.error('Error during base price calculation by zone:', error);
        }
      }

      order.basePrice = basePrice;
      let createdOrder: Order | null = null;

      if (isPlacingDraft) {
        await this.orderRepository.update(order.id, tenantId, order);
        createdOrder = await this.orderRepository.findById(order.id);
        if (!createdOrder) {
          throw new Error(`Updated order with ID ${order.id} not found`);
        }
      } else {
        createdOrder = await this.orderRepository.create(order);
      }
      if (!isPlacingDraft) {
        await this.orderStatusHistoryRepository.create(
          createdOrder.id,
          null,
          createdOrder.status,
          userId,
          'Order created',
        );
      }
      await this.historyService.create({
        tenantId,
        entity: HISTORY_ENTITIES.ORDER,
        entityId: createdOrder.id,
        property: 'status',
        propertyDataType: 'string',
        oldValue: createdOrder.status,
        newValue: OrderStatus.Submitted,
        createdBy: userId,
        updatedBy: userId,
      });

      if (createOrderDto.items && createOrderDto.items.length > 0) {
        if (isPlacingDraft)
          await this.orderItemRepository.deleteByOrderId(createdOrder.id);
        await this.orderItemRepository.createBulk(
          createdOrder.id,
          createOrderDto.items,
        );

        const totals = await this.orderItemRepository.calculateOrderTotals(
          createdOrder.id,
        );

        if (totals) {
          createdOrder.totalItems = totals.totalItems;
          createdOrder.totalWeight = totals.totalWeight;
          createdOrder.totalVolume = totals.totalVolume;
          createdOrder.declaredValue = totals.totalDeclaredValue;

          await this.orderRepository.update(createdOrder.id, tenantId, {
            totalItems: totals.totalItems,
            totalWeight: totals.totalWeight,
            totalVolume: totals.totalVolume,
            declaredValue: totals.totalDeclaredValue,
          });
        }
      }

      if (createOrderDto.scheduledCollectionTime) {
        await this.orderStopHistoryRepository.create(
          createdOrder.id,
          OrderStopType.Collection,
          createOrderDto.scheduledCollectionTime,
          userId,
        );
      }

      if ((createOrderDto as any).scheduledDeliveryTime) {
        await this.orderStopHistoryRepository.create(
          createdOrder.id,
          OrderStopType.Delivery,
          (createOrderDto as any).scheduledDeliveryTime,
          userId,
        );
      }
      await queryRunner.commitTransaction();

      try {
        const orderDetailsForPricing: OrderDetailsDto = {
          id: createdOrder.id,
          basePrice: createdOrder.basePrice || 0,
          declaredPrice: createdOrder.declaredValue || 0,
          weight: createdOrder.totalWeight || 0,
          distance: createdOrder.distance || 0,
          items: createOrderDto?.items || [],
          quantity: createdOrder.totalItems || 1,
        };

        let setMembers: {
          memberId: string;
          isGroup: boolean;
          configuration: ConfigurationType;
        }[] = [];

        if (createdOrder.priceSetId) {
          setMembers = await this.orderRepository.getModifierIdsBySetId(
            createdOrder.priceSetId,
          );
        }
        const calculationResult =
          await this.priceModifiersService.calculatePrice(
            tenantId,
            orderDetailsForPricing,
            setMembers,
          );
        const selectedModifiers =
          createOrderDto?.calculationResult?.selectedModifiers || [];

        const disSelectedModifiers =
          createOrderDto?.calculationResult?.disSelectedModifiers || [];

        const disSelectedModifierIds =
          createOrderDto?.calculationResult?.disSelectedModifiers?.map(
            (m) => m.id,
          ) || [];

        const allModifiersMap = new Map<string, any>();

        selectedModifiers.forEach((modifier) => {
          allModifiersMap.set(modifier.id, {
            ...modifier,
            hasCustomerSelected: !disSelectedModifierIds.includes(modifier.id),
            createdAt: new Date(),
          });
        });

        disSelectedModifiers.forEach((modifier) => {
          if (!allModifiersMap.has(modifier.id)) {
            allModifiersMap.set(modifier.id, {
              ...modifier,
              hasCustomerSelected: false,
              createdAt: new Date(),
            });
          }
        });

        const enrichedModifiers = Array.from(allModifiersMap.values());

        const updatedTotalModifierPrice = enrichedModifiers
          .filter((m) => m.hasCustomerSelected)
          .reduce((sum, m) => sum + m.amount, 0);

        const updatedTotalPrice =
          calculationResult.basePrice + updatedTotalModifierPrice;

        // 4. Build final cleaned result
        const cleanedResult = {
          ...calculationResult,
          priceSetId: createOrderDto?.calculationResult?.priceSetId,
          totalModifierPrice: updatedTotalModifierPrice,
          totalPrice: updatedTotalPrice,
          modifiers: enrichedModifiers,
          // modifiers: userApprovedModifiers.map(({ children, ...rest }) => ({
          //   ...rest,
          //   createdAt: new Date(), // include createdAt
          // })),
          errors: undefined,
        };

        createdOrder.pricingSummary = cleanedResult;
        createdOrder.customPricingSummary = cleanedResult;
        if (calculationResult) {
          createdOrder.basePrice = calculationResult.basePrice;
          createdOrder.totalPrice = calculationResult.totalPrice;

          let optionsPrice = 0;
          calculationResult.modifiers.forEach((modifier) => {
            optionsPrice += modifier.amount;
          });
          createdOrder.optionsPrice = optionsPrice;

          await this.orderRepository.update(createdOrder.id, tenantId, {
            basePrice: calculationResult.basePrice,
            optionsPrice: optionsPrice,
            totalPrice: calculationResult.totalPrice,
            pricingSummary: cleanedResult,
            customPricingSummary: cleanedResult,
          });
        }
      } catch (priceError) {
        console.error('Error calculating price for order:', priceError);
      }

      // Update status to Placed
      await this.orderRepository.update(createdOrder.id, tenantId, {
        status: OrderStatus.Submitted,
      });

      await this.orderStatusHistoryRepository.create(
        createdOrder.id,
        createdOrder.status,
        OrderStatus.Submitted,
        userId,
        isPlacingDraft ? 'Draft order placed' : 'Order placed',
      );

      const orderItems = await this.orderItemRepository.findByOrderId(
        createdOrder.id,
      );
      const orderDetails = await this.findOne(tenantId, createdOrder.id);

      this.eventEmitter.emit('order.created', {
        order: {
          ...orderDetails,
          items: orderItems,
          collection: {
            contactName: createdOrder.collectionContactName,
            date: createdOrder.scheduledCollectionTime,
          },
          delivery: {
            contactName: createdOrder.deliveryContactName,
            date: createdOrder.scheduledDeliveryTime,
          },
        },
        tenantId: createdOrder.tenantId,
      });

      const orderResponse = this.mapOrderToResponseDto(createdOrder);

      return orderResponse;
    } catch (error) {
      await queryRunner.rollbackTransaction();

      console.error('DEBUG - Order creation failed with error:', error);

      if (error instanceof OrderDeliveryTimeInvalidException) {
        throw error;
      }

      if (error.message && error.message.includes('foreign key constraint')) {
        console.error(
          'DEBUG - Foreign key constraint violation details:',
          error,
        );

        const constraintMatch = error.message.match(/constraint "([^"]+)"/);
        const constraintName = constraintMatch ? constraintMatch[1] : 'unknown';

        throw new OrderCreationFailedException(
          `Foreign key constraint violation (${constraintName}). One or more referenced entities do not exist.`,
        );
      }

      if (
        error.message &&
        error.message.includes('violates foreign key constraint')
      ) {
        console.error('DEBUG - Foreign key issue details:', error.message);

        if (error.message.includes('assigned_driver_id')) {
          throw new OrderCreationFailedException(
            'Error with assigned driver ID. This field is optional and can be left empty during order creation.',
          );
        } else {
          throw new OrderCreationFailedException(error.message);
        }
      } else {
        throw new OrderCreationFailedException(error.message);
      }
    } finally {
      await queryRunner.release();
    }
  }

  async findAll(
    tenantId: string,
    filter: BaseFilterDto,
  ): Promise<PaginatedResult<OrderResponseDto>> {
    return this.orderRepository.findByTenantId(tenantId, filter);
  }

  async findOne(tenantId: string, id: string): Promise<OrderDetailResponseDto> {
    const order = await this.orderRepository.findById(id);

    if (!order) {
      throw new OrderNotFoundException(id);
    }

    if (order.tenantId !== tenantId) {
      throw new OrderTenantMismatchException(id, tenantId);
    }

    const items = await this.orderItemRepository.findByOrderId(id);

    const statusHistory =
      await this.orderStatusHistoryRepository.findByOrderId(id);

    const assignmentHistory =
      await this.orderAssignmentHistoryRepository.findByOrderId(id);

    const stopHistory = await this.orderStopHistoryRepository.findByOrderId(id);

    const orderResponse = this.mapOrderToDetailResponseDto(order);

    const attachments = await this.getOrderAttachments(id);

    orderResponse.attachments = attachments;

    if (items && items.length > 0) {
      orderResponse.items = items.map((item) => ({
        id: item.id,
        orderId: item.orderId,
        packageTemplateId: item.packageTemplateId,
        packageTemplateName: item.packageTemplateName,
        itemType: item.itemType,
        quantity: item.quantity,
        totalWeight: item.totalWeight,
        weightUnit: item.weightUnit,
        length: item.length,
        width: item.width,
        height: item.height,
        dimensionUnit: item.dimensionUnit,
        volume: item.volume,
        declaredValue: item.declaredValue,
        description: item.description,
        notes: item.notes,
        imageName: item.imageName,
        imageUrl:
          item.imageUrl && this.fileUploadService.getPublicUrl(item.imageUrl),
        createdAt: item.createdAt,
        updatedAt: item.updatedAt,
      }));
    }

    if (statusHistory && statusHistory.length > 0) {
      orderResponse.statusHistory = statusHistory.map((history) => ({
        id: history.id,
        orderId: history.orderId,
        previousStatus: history.previousStatus,
        newStatus: history.newStatus,
        reason: history.reason,
        comments: history.comments,
        changedBy: history.changedBy,
        changedAt: history.changedAt,
      }));
    }

    if (assignmentHistory && assignmentHistory.length > 0) {
      orderResponse.assignmentHistory = assignmentHistory.map((history) => ({
        id: history.id,
        orderId: history.orderId,
        previousDriverId: history.previousAssigneeId || '',
        newDriverId: history.newAssigneeId || '',
        previousVehicleId: history.previousVehicleId || '',
        newVehicleId: history.assignedVehicleId || '',
        reason: history.reason,
        assignedBy: history.assignedById,
        assignedAt: history.createdAt,
      }));
    }

    if (stopHistory && stopHistory.length > 0) {
      orderResponse.stopHistory = stopHistory.map((history) => ({
        id: history.id,
        orderId: history.orderId,
        stopType: history.stopType,
        scheduledTime: history.scheduledTime,
        actualTime: history.actualTime,
        locationData: history.locationData,
        notes: history.notes,
        updatedBy: history.createdBy,
        updatedAt: history.createdAt,
      }));
    }

    return orderResponse;
  }

  async findByTrackingNumber(
    tenantId: string,
    trackingNumber: string,
  ): Promise<OrderDetailResponseDto> {
    const order =
      await this.orderRepository.findByTrackingNumber(trackingNumber);

    if (!order) {
      throw new OrderNotFoundException(
        `Order with tracking number: ${trackingNumber} not found`,
      );
    }

    if (order.tenantId !== tenantId) {
      throw new OrderTenantMismatchException(order.id, tenantId);
    }

    return this.findOne(tenantId, order.id);
  }

  async findByTrackingNumberWithoutAuth(
    trackingNumber: string,
  ): Promise<NullableType<Order>> {
    const order =
      await this.orderRepository.findByTrackingNumber(trackingNumber);

    if (!order) {
      throw new OrderNotFoundException(
        `Order with tracking number: ${trackingNumber} not found`,
      );
    }

    return this.orderRepository.findByTrackingNumber(trackingNumber);
  }

  async update(
    tenantId: string,
    id: string,
    userId: string,
    updateOrderDto: UpdateOrderDto,
    isCustomer: boolean,
    customerId?: string,
  ): Promise<OrderResponseDto> {
    const order = await this.orderRepository.findById(id);

    if (!order || (isCustomer && order.customerId !== customerId)) {
      throw new OrderNotFoundException(id);
    }

    const orderStatusHistory =
      await this.orderStatusHistoryRepository.findSubmitedByOrderId(id);

    if (isCustomer && orderStatusHistory) {
      const currentTime = new Date();
      const createdTime = new Date(orderStatusHistory.changedAt);
      const timeDiffInMinutes =
        (currentTime.getTime() - createdTime.getTime()) / (1000 * 60);

      if (
        order.status === OrderStatus.Submitted &&
        timeDiffInMinutes > this.MAX_MINUTES &&
        (updateOrderDto.internalNotes === '' ||
          updateOrderDto.internalNotes === order.internalNotes)
      ) {
        throw new OrderUpdateBadRequestException(
          'Order can only be updated within 10 minutes of submission',
        );
      } else if (updateOrderDto.internalNotes !== order.internalNotes) {
        const orderDto = new UpdateOrderDto();
        orderDto.internalNotes = updateOrderDto.internalNotes;
        updateOrderDto = orderDto;
      }
    }

    if (order.tenantId !== tenantId) {
      throw new OrderTenantMismatchException(id, tenantId);
    }
    if (order.isLocked)
      throw new OrderLockedException(id, order.lockedBy || '');
    // Validate time logic
    if (
      updateOrderDto.scheduledCollectionTime &&
      updateOrderDto.scheduledDeliveryTime &&
      updateOrderDto.scheduledDeliveryTime <=
        updateOrderDto.scheduledCollectionTime
    ) {
      throw new OrderDeliveryTimeInvalidException();
    }

    const queryRunner = this.connection.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      const updateData: Partial<Order> = { updatedBy: userId };
      Object.assign(updateData, {
        referenceNumber: updateOrderDto.referenceNumber,
        packageTemplateId: updateOrderDto.packageTemplateId,
        collectionAddressId: updateOrderDto.collectionAddressId,
        collectionContactName: updateOrderDto.collectionContactName,
        collectionInstructions: updateOrderDto.collectionInstructions,
        collectionSignatureRequired: updateOrderDto.collectionSignatureRequired,
        scheduledCollectionTime: updateOrderDto.scheduledCollectionTime,
        actualCollectionTime: updateOrderDto.actualCollectionTime,
        collectionZoneId: updateOrderDto.collectionZoneId,
        deliveryAddressId: updateOrderDto.deliveryAddressId,
        deliveryContactName: updateOrderDto.deliveryContactName,
        deliveryInstructions: updateOrderDto.deliveryInstructions,
        deliverySignatureRequired: updateOrderDto.deliverySignatureRequired,
        scheduledDeliveryTime: updateOrderDto.scheduledDeliveryTime,
        actualDeliveryTime: updateOrderDto.actualDeliveryTime,
        deliveryZoneId: updateOrderDto.deliveryZoneId,
        vehicleTypeId: updateOrderDto.vehicleTypeId,
        assignedDriverId: updateOrderDto.assignedDriverId,
        assignedVehicleId: updateOrderDto.assignedVehicleId,
        codAmount: updateOrderDto.codAmount,
        declaredValue: updateOrderDto.declaredValue,
        codCollected: updateOrderDto.codCollected,
        codCollectionDate: updateOrderDto.codCollectionDate,
        basePriceType: updateOrderDto.basePriceType,
        // basePrice: updateOrderDto.basePrice,
        miscAdjustment: updateOrderDto.miscAdjustment,
        customerAdjustment: updateOrderDto.customerAdjustment,
        description: updateOrderDto.description,
        comments: updateOrderDto.comments,
        internalNotes: updateOrderDto.internalNotes,
        customFields: updateOrderDto.customFields,
        submittedAt: updateOrderDto.submittedAt,
        metadata: updateOrderDto.metadata,
      });

      // History logging
      // ✅ Apply custom pricing modifiers
      if (
        updateOrderDto.customModifierName &&
        updateOrderDto.customModifierAmount !== undefined
      ) {
        order.updatedAt = new Date();
        this.applyCustomPricingModifier(
          order,
          {
            name: updateOrderDto.customModifierName,
            amount: updateOrderDto.customModifierAmount,
          },
          userId,
        );
        updateData.updatedAt = order.updatedAt;
        updateData.miscAdjustment = order.miscAdjustment;
        updateData.customTotalPrice = order.customTotalPrice;
        updateData.customPricingSummary = order.customPricingSummary;
      }
      const currentOrder = await this.orderRepository.findById(id);
      if (currentOrder) {
        for (const updateFieldKey in updateOrderDto) {
          let oldValue = currentOrder[updateFieldKey];
          let newValue = updateData[updateFieldKey];
          const isValueChanged = oldValue?.toString() !== newValue?.toString();

          if (!isValueChanged) continue;

          if (
            updateFieldKey === 'assignedVehicleId' &&
            updateOrderDto.assignedVehicleId
          ) {
            const newVehicle = await this.vehiclesService.getVehicleDetails(
              updateOrderDto.assignedVehicleId,
            );
            const oldVehicle = currentOrder.assignedVehicleId
              ? await this.vehiclesService.getVehicleDetails(
                  currentOrder.assignedVehicleId,
                )
              : null;
            newValue = `${newVehicle?.model} - ${newVehicle?.make}(${newVehicle?.vehicleType.name})`;
            oldValue = oldValue
              ? `${oldVehicle?.model} - ${oldVehicle?.make}(${oldVehicle?.vehicleType.name})`
              : null;
          }

          const propertyDataType = getHistoryPropertyType(updateFieldKey);

          await this.historyService.create({
            tenantId,
            entity: HISTORY_ENTITIES.ORDER,
            entityId: id,
            property: updateFieldKey,
            oldValue: oldValue,
            propertyDataType: propertyDataType,
            newValue: newValue,
            createdBy: userId,
            updatedBy: userId,
          });
        }
      }

      const updatedOrder = await this.orderRepository.update(
        id,
        tenantId,
        updateData,
      );

      if (!updatedOrder) {
        throw new OrderUpdateFailedException(
          id,
          'Failed to update order details',
        );
      }

      if (updateOrderDto.status && updateOrderDto.status !== order.status) {
        const statusUpdated = await this.orderStatusService.updateOrderStatus(
          updatedOrder,
          updateOrderDto.status,
          userId,
          'Status updated via order update',
        );

        if (!statusUpdated) {
          throw new OrderUpdateFailedException(
            id,
            'Failed to update order status',
          );
        }
      }

      if (updateOrderDto.items && updateOrderDto.items.length > 0) {
        await this.orderItemRepository.bulkUpdate(
          updateOrderDto.items,
          order.id,
        );

        const totals = await this.orderItemRepository.calculateOrderTotals(id);
        if (totals) {
          await this.orderRepository.update(id, tenantId, {
            totalItems: totals.totalItems,
            totalWeight: totals.totalWeight,
            totalVolume: totals.totalVolume,
            declaredValue: totals.totalDeclaredValue,
            updatedBy: userId,
          });
        }
      }
      // Stop history
      if (updateOrderDto.scheduledCollectionTime) {
        await this.orderStopHistoryRepository.create(
          id,
          OrderStopType.Collection,
          updateOrderDto.scheduledCollectionTime,
          userId,
        );
      }
      if (updateOrderDto.scheduledDeliveryTime) {
        await this.orderStopHistoryRepository.create(
          id,
          OrderStopType.Delivery,
          updateOrderDto.scheduledDeliveryTime,
          userId,
        );
      }

      // Status update
      if (updateOrderDto.status && updateOrderDto.status !== order.status) {
        const updated = await this.orderStatusService.updateOrderStatus(
          order,
          updateOrderDto.status,
          userId,
          'Status updated via order update',
        );
        if (!updated)
          throw new OrderUpdateFailedException(id, 'Failed to update status');
      }

      // Accept pricing logic
      //  const acceptPriceChanges = updateOrderDto.acceptPriceChanges;
      const finalOrder: any = await this.orderRepository.findById(id);
      const items = await this.orderItemRepository.findByOrderId(id);

      const orderDetailsForPricing: OrderDetailsDto = {
        id: finalOrder.id,
        basePrice: finalOrder.basePrice || 0,
        declaredPrice: finalOrder.declaredValue || 0,
        weight: finalOrder.totalWeight || 0,
        distance: finalOrder.distance || 0,
        items,
        quantity: finalOrder.totalItems || 1,
      };

      // const effectivePriceSetId: any = order.newPriceSetId || order.priceSetId;
      const effectivePriceSetId: any =
        updateOrderDto.priceSetId ?? order.newPriceSetId ?? order.priceSetId;
      const modifierSet =
        await this.orderRepository.getModifierIdsBySetId(effectivePriceSetId);

      const collectionAddress = await this.addressService.getAddressDetails(
        finalOrder.collectionAddressId,
      );
      const deliveryAddress = await this.addressService.getAddressDetails(
        finalOrder.deliveryAddressId,
      );

      let calculatedDistance = 0;
      if (collectionAddress && deliveryAddress) {
        try {
          calculatedDistance = await calculateAddressDistanceById(
            this.addressService,
            finalOrder.collectionAddressId,
            finalOrder.deliveryAddressId,
            DistanceUnit.Kilometers,
            collectionAddress.latitude,
            collectionAddress.longitude,
            deliveryAddress.latitude,
            deliveryAddress.longitude,
          );
          finalOrder.distance = calculatedDistance;
          orderDetailsForPricing.distance = calculatedDistance;
        } catch (err) {
          console.log('Error calculating distance:', err);
        }
      }
      orderDetailsForPricing.distance = calculatedDistance;
      await this.orderRepository.update(id, tenantId, {
        distance: calculatedDistance,
        updatedBy: userId,
      });

      const collectionPrefix = collectionAddress?.postalCode
        ?.substring(0, 3)
        .toUpperCase();

      const deliveryPrefix = deliveryAddress?.postalCode
        ?.substring(0, 3)
        .toUpperCase();
      if (collectionPrefix && deliveryPrefix && effectivePriceSetId) {
        const originZone =
          await this.zoneRepository.findLatestUpdatedZoneByPostalCode(
            collectionPrefix,
            tenantId,
          );
        const destinationZone =
          await this.zoneRepository.findLatestUpdatedZoneByPostalCode(
            deliveryPrefix,
            tenantId,
          );

        if (originZone && destinationZone) {
          const zoneTable =
            await this.priceSetService.getBasePriceByZone(effectivePriceSetId);
          const matchedZone = await this.zoneTableRepository.findZoneValue(
            originZone.id,
            destinationZone.id,
            zoneTable.id,
          );
          if (updateOrderDto.basePrice != null) {
            finalOrder.basePrice = updateOrderDto.basePrice;
            orderDetailsForPricing.basePrice = updateOrderDto.basePrice;
          } else if (matchedZone?.value != null) {
            finalOrder.basePrice = matchedZone.value;
            orderDetailsForPricing.basePrice = matchedZone.value;
          } else if (finalOrder.basePrice != null) {
            orderDetailsForPricing.basePrice = finalOrder.basePrice;
          } else {
            finalOrder.basePrice = 0;
            orderDetailsForPricing.basePrice = 0;
          }
        }
      }
      const pricingCalcResult = await this.priceModifiersService.calculatePrice(
        tenantId,
        orderDetailsForPricing,
        modifierSet,
      );

      let totalModifierPrice = 0;

      if (updateOrderDto.priceSetId || finalOrder.newPriceSetId) {
        totalModifierPrice = pricingCalcResult.modifiers
          .filter(
            (mod) =>
              mod.configuration === 'required' ||
              mod.configuration === 'selected',
          )
          .reduce((sum, mod) => sum + mod.amount, 0);
      } else {
        const selectedModifierIds = new Set(
          (order.customPricingSummary?.modifiers ?? [])
            .filter((m) => m.hasCustomerSelected)
            .map((m) => m.id),
        );

        totalModifierPrice = pricingCalcResult.modifiers
          .filter((mod) => selectedModifierIds.has(mod.id))
          .reduce((sum, mod) => sum + mod.amount, 0);
      }
      const newTotalPrice = Number(finalOrder.basePrice) + totalModifierPrice;

      const allCustomModifiers =
        order.customPricingSummary?.customModifiers ?? [];
      const committedModifiers = allCustomModifiers.map((m) => ({
        ...m,
        committed: true,
      }));

      const customModifierTotal = committedModifiers.reduce(
        (sum, m) => sum + m.amount,
        0,
      );
      const newCustomPrice = newTotalPrice + customModifierTotal;
      const acceptPriceChanges = updateOrderDto.acceptPriceChanges === true;

      if (acceptPriceChanges) {
        const committedModifiers = (
          order.customPricingSummary?.customModifiers ?? []
        ).map((m) => ({ ...m, committed: true }));

        const newCustomPricingSummary = {
          ...order.customPricingSummary,
          basePrice: finalOrder.basePrice,
          // basePrice: pricingCalcResult.basePrice,
          totalModifierPrice,
          priceSetId: order.newPriceSetId,
          totalPrice: newCustomPrice,
          customModifiers: committedModifiers,
          modifiers: pricingCalcResult.modifiers.map(({ children, ...mod }) => {
            const existing = order.customPricingSummary?.modifiers?.find(
              (old) => old.id === mod.id,
            );

            return {
              ...mod,
              hasCustomerSelected:
                existing?.hasCustomerSelected ??
                (mod.configuration === 'required' ||
                  mod.configuration === 'selected'),
            };
          }),
        };

        const finalPriceSetId =
          finalOrder.newPriceSetId ?? finalOrder.priceSetId;

        await this.orderRepository.update(id, tenantId, {
          customPricingSummary: newCustomPricingSummary,
          optionsPrice: totalModifierPrice,
          totalPrice: newCustomPrice,
          priceSetId: finalPriceSetId,
          basePrice: finalOrder.basePrice,
          newPriceSetId: null,
          updatedBy: userId,
        });
      } else {
        const updateFields: any = {
          basePrice: pricingCalcResult.basePrice,
          optionsPrice: totalModifierPrice,
          updatedBy: userId,
        };

        // Only update newPriceSetId if it's explicitly passed
        if (updateOrderDto.priceSetId !== undefined) {
          updateFields.newPriceSetId = updateOrderDto.priceSetId;
        }

        await this.orderRepository.update(id, tenantId, updateFields);
      }

      await queryRunner.commitTransaction();

      const orderItems = await this.orderItemRepository.findByOrderId(id);

      const final: any = await this.orderRepository.findById(id);
      const response = this.mapOrderToResponseDto(final);
      response.items = orderItems.map((item) => ({
        id: item.id,
        orderId: item.orderId,
        packageTemplateId: item.packageTemplateId,
        itemType: item.itemType,
        quantity: item.quantity,
        weight: item.totalWeight,
        weightUnit: item.weightUnit,
        length: item.length,
        width: item.width,
        height: item.height,
        dimensionUnit: item.dimensionUnit,
        volume: item.volume,
        declaredValue: item.declaredValue,
        description: item.description,
        notes: item.notes,
        imageUrl: item.imageUrl,
        createdAt: item.createdAt,
        updatedAt: item.updatedAt,
      }));

      return response;
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw new OrderUpdateFailedException(id, error.message);
    } finally {
      await queryRunner.release();
    }
  }

  private applyCustomPricingModifier(
    order: Order,
    newModifier: { name: string; amount: number },
    userId: string,
  ): void {
    const original = order.pricingSummary;

    if (!original) {
      throw new Error('Original pricingSummary not found for the order');
    }

    //  Step 1: Initialize customPricingSummary if not present
    if (!order.customPricingSummary) {
      order.customPricingSummary = {
        ...original,
        customModifiers: [],
        customTotalPrice: original.totalPrice ?? 0,
      };
    }

    //  Step 2: Reassign to a local variable to satisfy TypeScript
    const customSummary: any = order.customPricingSummary;

    const existingCustomModifiers: Array<{
      name: string;
      amount: number;
      createdAt?: Date;
      committed?: boolean;
    }> = customSummary.customModifiers ?? [];

    const existingNames = new Set(
      existingCustomModifiers.map((mod) => mod.name.toLowerCase()),
    );

    if (existingNames.has(newModifier.name.toLowerCase())) {
      throw new CustomModifierAlreadyExistsException(
        newModifier.name,
        order.id,
      );
    }

    const modifierToAdd = {
      ...newModifier,
      createdAt: new Date(),
      committed: false,
    };

    const updatedCustomModifiers = [...existingCustomModifiers, modifierToAdd];

    const customModifiersTotal = updatedCustomModifiers.reduce(
      (sum, m) => sum + m.amount,
      0,
    );

    const originalTotalPrice = original.totalPrice ?? 0;

    //  Step 3: Update the object via `customSummary`
    order.customPricingSummary = {
      ...customSummary,
      customModifiers: updatedCustomModifiers,
      customTotalPrice: originalTotalPrice + customModifiersTotal,
    };

    //  Step 4: Update flat fields
    order.customTotalPrice = originalTotalPrice + customModifiersTotal;
    order.miscAdjustment = customModifiersTotal;
    order.updatedBy = userId;
  }

  async getOrderPricePreview(
    orderId: string,
    tenantId: string,
  ): Promise<OrderPricePreviewDto> {
    const order = await this.orderRepository.findById(orderId);
    if (!order) throw new OrderNotFoundException(orderId);
    if (order.tenantId !== tenantId)
      throw new OrderTenantMismatchException(orderId, tenantId);

    const customSummary = order.customPricingSummary;
    const oldBasePrice = Number(
      customSummary?.basePrice ?? order.basePrice ?? 0,
    );
    const oldModifiers = customSummary?.modifiers ?? [];
    const allCustomModifiers = customSummary?.customModifiers ?? [];

    //  New logic using committed flag
    const oldCustomModifiers = allCustomModifiers.filter((m) => m.committed);
    const newCustomModifiers = allCustomModifiers.filter((m) => !m.committed);

    const oldModifierTotal = oldModifiers.reduce((sum, m) => sum + m.amount, 0);
    const oldCustomModifierTotal = oldCustomModifiers.reduce(
      (sum, m) => sum + m.amount,
      0,
    );

    const oldTotalPrice = Number(
      (customSummary?.totalPrice ?? oldBasePrice + oldModifierTotal).toFixed(2),
    );

    const oldCustomTotalPrice =
      oldCustomModifiers.length > 0
        ? Number((oldTotalPrice + oldCustomModifierTotal).toFixed(2))
        : oldTotalPrice;

    const isPriceSetFinalized = !order.newPriceSetId;
    const pricingUnchanged =
      isPriceSetFinalized &&
      order.totalPrice === oldCustomTotalPrice &&
      newCustomModifiers.length === 0;

    if (pricingUnchanged) {
      return {
        orderId: order.id,
        priceSetChanged: false,
        changedFields: [],
        pricing: {
          old: {
            basePrice: oldBasePrice,
            totalPrice: oldTotalPrice,
            customTotalPrice: oldCustomTotalPrice,
            modifiers: oldModifiers,
            customModifiers: oldCustomModifiers,
          },
          new: {
            basePrice: oldBasePrice,
            totalPrice: oldTotalPrice,
            customTotalPrice: oldCustomTotalPrice,
            modifiers: oldModifiers,
            customModifiers: [...oldCustomModifiers],
          },
        },
      };
    }

    const items = await this.orderItemRepository.findByOrderId(order.id);

    const collectionAddress = await this.addressService.getAddressDetails(
      order.collectionAddressId,
    );
    const deliveryAddress = await this.addressService.getAddressDetails(
      order.deliveryAddressId,
    );

    let calculatedDistance = 0;
    if (collectionAddress && deliveryAddress) {
      try {
        calculatedDistance = await calculateAddressDistanceById(
          this.addressService,
          order.collectionAddressId,
          order.deliveryAddressId,
          DistanceUnit.Kilometers,
          collectionAddress.latitude,
          collectionAddress.longitude,
          deliveryAddress.latitude,
          deliveryAddress.longitude,
        );
      } catch (error) {
        console.log(
          'Error calculating distance in getOrderPricePreview:',
          error,
        );
      }
    }
    const orderDetails: OrderDetailsDto = {
      id: order.id,
      basePrice: oldBasePrice,
      declaredPrice: order.declaredValue ?? 0,
      weight: order.totalWeight ?? 0,
      distance: calculatedDistance || order.distance || 0,
      quantity: order.totalItems ?? 1,
      items,
      collectionAddressId: order.collectionAddressId,
      deliveryAddressId: order.deliveryAddressId,
    };

    const effectivePriceSetId = order.newPriceSetId ?? order.priceSetId;
    const modifierSet =
      await this.orderRepository.getModifierIdsBySetId(effectivePriceSetId);

    const collectionPrefix = collectionAddress?.postalCode
      ?.substring(0, 3)
      ?.toUpperCase();
    const deliveryPrefix = deliveryAddress?.postalCode
      ?.substring(0, 3)
      ?.toUpperCase();
    let newBasePrice = oldBasePrice;

    if (collectionPrefix && deliveryPrefix && effectivePriceSetId) {
      const originZone =
        await this.zoneRepository.findLatestUpdatedZoneByPostalCode(
          collectionPrefix,
          tenantId,
        );
      const destinationZone =
        await this.zoneRepository.findLatestUpdatedZoneByPostalCode(
          deliveryPrefix,
          tenantId,
        );

      if (originZone && destinationZone) {
        const zoneTable =
          await this.priceSetService.getBasePriceByZone(effectivePriceSetId);
        const matchedZone = zoneTable.zoneTableValues?.find(
          (z) =>
            z.originZoneId === originZone.id &&
            z.destinationZoneId === destinationZone.id,
        );
        if (order.basePrice !== undefined) {
          newBasePrice = Number(order.basePrice);
        } else if (matchedZone?.value != null) {
          newBasePrice = Number(matchedZone.value);
        } else {
          newBasePrice = 0;
        }

        orderDetails.basePrice = newBasePrice;
      }
    }

    const pricingResult = await this.priceModifiersService.calculatePrice(
      tenantId,
      orderDetails,
      modifierSet,
    );
    let newModifiers: any[] = [];

    if (order.newPriceSetId) {
      newModifiers = pricingResult.modifiers.filter(
        (mod) =>
          mod.configuration === 'required' || mod.configuration === 'selected',
      );
    } else {
      const selectedModifierIds = new Set(
        (customSummary?.modifiers ?? [])
          .filter((m) => m.hasCustomerSelected)
          .map((m) => m.id),
      );

      newModifiers = pricingResult.modifiers.filter((mod) =>
        selectedModifierIds.has(mod.id),
      );
    }

    const newModifierTotal = newModifiers.reduce(
      (sum, mod) => sum + mod.amount,
      0,
    );
    const allCustomModifierTotal = [
      ...oldCustomModifiers,
      ...newCustomModifiers,
    ].reduce((sum, m) => sum + m.amount, 0);

    const basePlusModifierPrice = Number(
      (Number(newBasePrice) + Number(newModifierTotal)).toFixed(2),
    );

    const newTotalPrice =
      allCustomModifiers.length > 0
        ? Number((basePlusModifierPrice + allCustomModifierTotal).toFixed(2))
        : basePlusModifierPrice;

    const newTotalPriceForModifiers = Number(
      (Number(newBasePrice) + Number(newModifierTotal)).toFixed(2),
    );
    const newCustomTotalPrice = Number(
      (newTotalPriceForModifiers + allCustomModifierTotal).toFixed(2),
    );

    const changedFields: { field: string; oldValue: any; newValue: any }[] = [];

    if (order.newPriceSetId) {
      changedFields.push({
        field: 'priceSetId',
        oldValue: order.priceSetId,
        newValue: order.newPriceSetId,
      });
    }

    if (oldBasePrice !== newBasePrice) {
      changedFields.push({
        field: 'basePrice',
        oldValue: oldBasePrice,
        newValue: newBasePrice,
      });
    }

    if (oldCustomTotalPrice !== newCustomTotalPrice) {
      changedFields.push({
        field: 'totalPrice',
        oldValue: oldCustomTotalPrice,
        newValue: newCustomTotalPrice,
      });
    }

    if (Number(customSummary?.totalModifierPrice ?? 0) !== newModifierTotal) {
      changedFields.push({
        field: 'totalModifierPrice',
        oldValue: customSummary?.totalModifierPrice ?? 0,
        newValue: newModifierTotal,
      });
    }

    if (newCustomModifiers.length > 0) {
      changedFields.push({
        field: 'customModifiers',
        oldValue: oldCustomModifiers,
        newValue: [...oldCustomModifiers, ...newCustomModifiers],
      });
    }

    return {
      orderId: order.id,
      priceSetChanged: !!order.newPriceSetId,
      changedFields,
      pricing: {
        old: {
          basePrice: oldBasePrice,
          totalPrice: oldTotalPrice,
          customTotalPrice: oldCustomTotalPrice,
          modifiers: oldModifiers,
          customModifiers: oldCustomModifiers,
        },
        new: {
          basePrice: newBasePrice,
          totalPrice: newTotalPrice,
          customTotalPrice: newCustomTotalPrice,
          modifiers: newModifiers.map(({ children, ...mod }) => {
            const existing = customSummary?.modifiers?.find(
              (m) => m.id === mod.id,
            );
            return {
              ...mod,
              hasCustomerSelected:
                existing?.hasCustomerSelected ??
                (mod.configuration === 'required' ||
                  mod.configuration === 'selected'),
            };
          }),
          customModifiers: [...oldCustomModifiers, ...newCustomModifiers],
        },
      },
    };
  }

  async remove(tenantId: string, id: string, userId: string): Promise<void> {
    const order = await this.orderRepository.findById(id);

    if (!order) {
      throw new OrderNotFoundException(id);
    }

    if (order.tenantId !== tenantId) {
      throw new OrderTenantMismatchException(id, tenantId);
    }

    if (order.isLocked) {
      throw new OrderLockedException(id, order.lockedBy || '');
    }

    const deleted = await this.orderRepository.softDelete(id, tenantId, userId);

    if (!deleted) {
      throw new OrderUpdateFailedException(id, 'Failed to delete order');
    }
  }

  async restore(
    tenantId: string,
    id: string,
    userId: string,
  ): Promise<OrderResponseDto> {
    const restored = await this.orderRepository.restore(id, tenantId, userId);

    if (!restored) {
      throw new OrderNotFoundException(id);
    }

    const order = await this.orderRepository.findById(id);

    if (!order || order.tenantId !== tenantId) {
      throw new OrderNotFoundException(id);
    }

    const orderResponse = this.mapOrderToResponseDto(order);

    this.eventEmitter.emit('order.restored', {
      orderId: id,
      tenantId,
      userId,
    });

    return orderResponse;
  }

  async hardDelete(tenantId: string, id: string): Promise<void> {
    const order = await this.orderRepository.findById(id);

    if (!order) {
      throw new OrderNotFoundException(id);
    }

    if (order.tenantId !== tenantId) {
      throw new OrderTenantMismatchException(id, tenantId);
    }

    const queryRunner = this.connection.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      await this.orderItemRepository.deleteByOrderId(id);
      await this.orderStatusHistoryRepository.deleteByOrderId(id);
      await this.orderAssignmentHistoryRepository.deleteByOrderId(id);
      await this.orderStopHistoryRepository.deleteByOrderId(id);

      const deleted = await this.orderRepository.hardDelete(id, tenantId);

      if (!deleted) {
        throw new OrderUpdateFailedException(
          id,
          'Failed to permanently delete order',
        );
      }

      await queryRunner.commitTransaction();

      this.eventEmitter.emit('order.permanently.deleted', {
        orderId: id,
        tenantId,
      });
    } catch (error) {
      await queryRunner.rollbackTransaction();

      if (
        error instanceof OrderNotFoundException ||
        error instanceof OrderTenantMismatchException ||
        error instanceof OrderUpdateFailedException
      ) {
        throw error;
      }

      throw new OrderUpdateFailedException(
        id,
        `Failed to permanently delete order: ${error.message}`,
      );
    } finally {
      await queryRunner.release();
    }
  }

  async changeStatus(
    tenantId: string,
    id: string,
    userId: string,
    status: OrderStatus,
    reason?: string,
    comments?: string,
  ): Promise<OrderResponseDto> {
    const order = await this.orderRepository.findById(id);

    if (!order) {
      throw new OrderNotFoundException(id);
    }

    if (order.tenantId !== tenantId) {
      throw new OrderTenantMismatchException(id, tenantId);
    }

    if (order.isLocked) {
      throw new OrderLockedException(id, order.lockedBy || '');
    }

    const previousStatus = order.status;

    const updatedOrder = await this.orderStatusService.updateOrderStatus(
      order,
      status,
      userId,
      reason,
      comments,
    );

    if (!updatedOrder) {
      throw new InvalidOrderStatusTransitionException(
        order.id,
        order.status,
        status,
      );
    }

    const refreshedOrder = await this.orderRepository.findById(id);

    if (!refreshedOrder) {
      throw new OrderNotFoundException(order.id);
    }

    const orderItems = await this.orderItemRepository.findByOrderId(id);
    const orderDetails = await this.findOne(tenantId, id);

    this.eventEmitter.emit('order.status.changed', {
      order: {
        ...orderDetails,
        items: orderItems,
        previousStatus,
        currentStatus: status,
        statusNotes: comments || reason,
        collection: {
          contactName: refreshedOrder.collectionContactName ?? 'Contact',
          date: refreshedOrder.scheduledCollectionTime,
        },
        delivery: {
          contactName: refreshedOrder.deliveryContactName ?? 'Contact',
          date: refreshedOrder.scheduledDeliveryTime,
        },
      },
      tenantId,
      previousStatus,
      currentStatus: status,
      notes: comments || reason,
    });

    return this.mapOrderToResponseDto(refreshedOrder);
  }

  async addOrderItem(
    tenantId: string,
    orderId: string,
    userId: string,
    createItemDto: CreateOrderItemDto,
  ): Promise<OrderItem> {
    const order = await this.orderRepository.findById(orderId);

    if (!order) {
      throw new OrderNotFoundException(orderId);
    }

    if (order.tenantId !== tenantId) {
      throw new OrderTenantMismatchException(orderId, tenantId);
    }

    if (order.isLocked) {
      throw new OrderLockedException(orderId, order.lockedBy || '');
    }

    const newItem = await this.orderItemRepository.create(
      orderId,
      createItemDto,
    );

    if (createItemDto.imageUrl) {
      // createItemDto.imageUrl = createItemDto.imageUrl.originalname;
      const fileUploadResult = await this.fileUploadService.uploadFile(
        createItemDto.imageUrl,
        'order-item',
        newItem.id,
        'image',
        userId,
      );
      if (fileUploadResult.success) {
        console.log({ fileUploadResult });

        await this.orderItemRepository.update(newItem.id, {
          imageUrl: fileUploadResult.filename,
          imageName: fileUploadResult.originalName,
        });
      }
    }

    const totals = await this.orderItemRepository.calculateOrderTotals(orderId);

    if (totals) {
      await this.orderRepository.update(orderId, tenantId, {
        totalItems: totals.totalItems,
        totalWeight: totals.totalWeight,
        totalVolume: totals.totalVolume,
        declaredValue: totals.totalDeclaredValue,
        updatedBy: userId,
      });
    }

    return newItem;
  }

  async updateOrderItem(
    tenantId: string,
    orderId: string,
    userId: string,
    updateItemDto: UpdateOrderItemDto,
  ): Promise<OrderItem> {
    const order = await this.orderRepository.findById(orderId);

    if (!order) {
      throw new OrderNotFoundException(orderId);
    }

    if (order.tenantId !== tenantId) {
      throw new OrderTenantMismatchException(orderId, tenantId);
    }

    if (order.isLocked) {
      throw new OrderLockedException(orderId, order.lockedBy || '');
    }

    const item = await this.orderItemRepository.findById(updateItemDto.id);

    if (!item) {
      throw new OrderItemNotFoundException(updateItemDto.id);
    }

    if (item.orderId !== orderId) {
      throw new InvalidOrderDataException(
        `Item with ID ${updateItemDto.id} does not belong to order ${orderId}`,
        'itemId',
      );
    }

    if (updateItemDto.imageUrl) {
      await this.fileUploadService.deleteFile(updateItemDto.imageUrl, userId);

      const fileUploadResult = await this.fileUploadService.uploadFile(
        updateItemDto.imageUrl,
        'order-item',
        updateItemDto.id,
        'image',
        userId,
      );
      if (fileUploadResult.success) {
        updateItemDto.imageUrl = fileUploadResult.filename;
        updateItemDto.imageName = fileUploadResult.originalName;
      }
    }

    const updatedItem = await this.orderItemRepository.update(
      updateItemDto.id,
      updateItemDto,
    );

    if (!updatedItem) {
      throw new OrderUpdateFailedException(
        orderId,
        `Failed to update item ${updateItemDto.id}`,
      );
    }

    const totals = await this.orderItemRepository.calculateOrderTotals(orderId);
    console.log({
      totalItems: totals.totalItems,
      totalWeight: totals.totalWeight,
      totalVolume: totals.totalVolume,
      declaredValue: totals.totalDeclaredValue,
      updatedBy: userId,
    });

    if (totals) {
      await this.orderRepository.update(orderId, tenantId, {
        totalItems: totals.totalItems,
        totalWeight: totals.totalWeight,
        totalVolume: totals.totalVolume,
        declaredValue: totals.totalDeclaredValue,
        updatedBy: userId,
      });
    }

    return updatedItem;
  }

  async removeOrderItem(
    tenantId: string,
    orderId: string,
    userId: string,
    itemId: string,
  ): Promise<void> {
    const order = await this.orderRepository.findById(orderId);

    if (!order) {
      throw new OrderNotFoundException(orderId);
    }

    if (order.tenantId !== tenantId) {
      throw new OrderTenantMismatchException(orderId, tenantId);
    }

    if (order.isLocked) {
      throw new OrderLockedException(orderId, order.lockedBy || '');
    }

    const item = await this.orderItemRepository.findById(itemId);

    if (!item) {
      throw new OrderItemNotFoundException(itemId);
    }

    if (item.orderId !== orderId) {
      throw new InvalidOrderDataException(
        `Item with ID ${itemId} does not belong to order ${orderId}`,
        'itemId',
      );
    }

    const deleted = await this.orderItemRepository.delete(itemId);

    if (!deleted) {
      throw new OrderUpdateFailedException(
        orderId,
        `Failed to delete item ${itemId}`,
      );
    }

    const totals = await this.orderItemRepository.calculateOrderTotals(orderId);

    if (totals) {
      await this.orderRepository.update(orderId, tenantId, {
        totalItems: totals.totalItems,
        totalWeight: totals.totalWeight,
        totalVolume: totals.totalVolume,
        declaredValue: totals.totalDeclaredValue,
        updatedBy: userId,
      });
    }
  }

  async lockOrder(
    tenantId: string,
    id: string,
    userId: string,
    reason?: string,
  ): Promise<boolean> {
    const order = await this.orderRepository.findById(id);

    if (!order) {
      throw new OrderNotFoundException(id);
    }

    if (order.tenantId !== tenantId) {
      throw new OrderTenantMismatchException(id, tenantId);
    }

    if (order.isLocked) {
      throw new OrderLockedException(id, order.lockedBy || '');
    }

    return this.orderRepository.lockOrder(id, userId, reason);
  }

  async unlockOrder(
    tenantId: string,
    id: string,
    userId: string,
  ): Promise<boolean> {
    const order = await this.orderRepository.findById(id);

    if (!order) {
      throw new OrderNotFoundException(id);
    }

    if (order.tenantId !== tenantId) {
      throw new OrderTenantMismatchException(id, tenantId);
    }

    if (order.isLocked && order.lockedBy !== userId) {
      throw new OrderUpdateFailedException(
        id,
        `Order is locked by user ${order.lockedBy} and can only be unlocked by them or an admin`,
      );
    }

    return this.orderRepository.unlockOrder(id);
  }

  async getOrderStatusHistory(tenantId: string, id: string): Promise<any[]> {
    const order = await this.orderRepository.findById(id);

    if (!order) {
      throw new OrderNotFoundException(id);
    }

    if (order.tenantId !== tenantId) {
      throw new OrderTenantMismatchException(id, tenantId);
    }

    return this.orderStatusHistoryRepository.findByOrderId(id);
  }

  async getOrderAssignmentHistory(
    tenantId: string,
    id: string,
  ): Promise<any[]> {
    const order = await this.orderRepository.findById(id);

    if (!order) {
      throw new OrderNotFoundException(id);
    }

    if (order.tenantId !== tenantId) {
      throw new OrderTenantMismatchException(id, tenantId);
    }

    return this.orderAssignmentHistoryRepository.findByOrderId(id);
  }

  async getOrderStopHistory(tenantId: string, id: string): Promise<any[]> {
    const order = await this.orderRepository.findById(id);

    if (!order) {
      throw new OrderNotFoundException(id);
    }

    if (order.tenantId !== tenantId) {
      throw new OrderTenantMismatchException(id, tenantId);
    }

    return this.orderStopHistoryRepository.findByOrderId(id);
  }

  async createDraftOrder(
    tenantId: string,
    userId: string,
    draftOrderDto: DraftOrderDto,
  ): Promise<DraftOrderResponseDto> {
    const queryRunner = this.connection.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      let order: Order | any;

      // ✅ Check if this is an update to an existing draft
      if (draftOrderDto.id) {
        const existing = await this.orderRepository.findById(draftOrderDto.id);
        if (existing && existing.status === OrderStatus.Draft) {
          const updatedData: Partial<Order> = {
            ...existing,
            ...draftOrderDto,
            updatedBy: userId,
            updatedAt: new Date(),
          };

          order = await this.orderRepository.update(
            existing.id,
            tenantId,
            updatedData,
          );

          // ✅ Handle item updates
          if (draftOrderDto.items) {
            // Delete old items (or implement more granular diff logic if needed)
            await this.orderItemRepository.deleteByOrderId(existing.id);

            // Create new items
            await this.orderItemRepository.createBulk(
              existing.id,
              draftOrderDto.items,
            );
          }
        } else {
          throw new Error('Invalid draft ID or draft no longer exists');
        }
      } else {
        // ✅ New draft creation
        const trackingNumber =
          await this.trackingNumberService.generateTrackingNumber();

        order = new Order();
        order.tenantId = tenantId;
        order.trackingNumber = trackingNumber;
        order.referenceNumber = draftOrderDto.referenceNumber;
        order.customerId = draftOrderDto.customerId || '';
        order.requestedById = userId;
        order.submittedById = userId;

        order.collectionAddressId = draftOrderDto.collectionAddressId;
        order.collectionContactName = draftOrderDto.collectionContactName;
        order.collectionInstructions = draftOrderDto.collectionInstructions;

        order.deliveryAddressId = draftOrderDto.deliveryAddressId;
        order.deliveryContactName = draftOrderDto.deliveryContactName;
        order.deliveryInstructions = draftOrderDto.deliveryInstructions;

        order.isCod = draftOrderDto.isCod || false;
        order.codAmount = draftOrderDto.codAmount;
        order.isInsurance = draftOrderDto.isInsurance || false;
        order.declaredValue = draftOrderDto.declaredValue;
        order.internalNotes = draftOrderDto.internalNotes;

        order.description = draftOrderDto.description;
        order.comments = draftOrderDto.comments;

        order.optionsPrice = 0;
        order.miscAdjustment = 0;
        order.customerAdjustment = 0;
        order.billingStatus = BillingStatus.NotBilled;
        order.paymentStatus = PaymentStatus.Pending;
        order.distanceUnit = DistanceUnit.Kilometers;

        order.customFields = {};
        order.metadata = {
          isDraft: true,
          draftCreatedAt: new Date().toISOString(),
          completionRequired: [
            'package_details',
            'pickup_time',
            'delivery_time',
            'pricing',
          ],
        };
        order.isLocked = false;
        order.isDeleted = false;
        order.createdBy = userId;
        order.submittedAt = new Date();

        order = await this.orderRepository.create(order);

        if (draftOrderDto.items && draftOrderDto.items.length > 0) {
          await this.orderItemRepository.createBulk(
            order.id,
            draftOrderDto.items,
          );
        }

        await this.orderStatusHistoryRepository.create(
          order.id,
          null,
          OrderStatus.Draft,
          userId,
          'Draft order created - awaiting completion',
          'Order created with basic pickup and delivery information',
        );

        this.eventEmitter.emit('order.draft.created', {
          orderId: order.id,
          tenantId: order.tenantId,
          customerId: order.customerId,
          trackingNumber: order.trackingNumber,
          userId,
        });

        await this.historyService.create({
          tenantId,
          entity: HISTORY_ENTITIES.ORDER,
          entityId: order.id,
          property: 'status',
          propertyDataType: 'string',
          newValue: OrderStatus.Draft,
          createdBy: userId,
          updatedBy: userId,
        });
      }

      await queryRunner.commitTransaction();
      return this.mapOrderToDraftResponseDto(order);
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw new OrderCreationFailedException(
        `Failed to create/update draft order: ${error.message}`,
      );
    } finally {
      await queryRunner.release();
    }
  }

  private mapOrderToDraftResponseDto(order: Order): DraftOrderResponseDto {
    const nextSteps: string[] = [];

    if (order.totalItems === 0) {
      nextSteps.push('Add package details and items');
    }
    if (!order.scheduledCollectionTime) {
      nextSteps.push('Set pickup/collection time');
    }
    if (!order.scheduledDeliveryTime) {
      nextSteps.push('Set delivery time');
    }
    if (order.basePrice === 0) {
      nextSteps.push('Configure pricing and options');
    }
    if (!order.packageTemplateId) {
      nextSteps.push('Select package template');
    }

    return {
      id: order.id,
      tenantId: order.tenantId,
      trackingNumber: order.trackingNumber,
      referenceNumber: order.referenceNumber,
      status: order.status,
      customerId: order.customerId,
      collectionAddressId: order.collectionAddressId,
      collectionContactName: order.collectionContactName,
      collectionInstructions: order.collectionInstructions,
      deliveryAddressId: order.deliveryAddressId,
      deliveryContactName: order.deliveryContactName,
      deliveryInstructions: order.deliveryInstructions,
      description: order.description,
      comments: order.comments,
      createdAt: order.createdAt,
      updatedAt: order.updatedAt,
      isDraft: true,
      nextSteps,
    };
  }

  async completeDraftOrder(
    tenantId: string,
    draftOrderId: string,
    userId: string,
    completeOrderDto: CreateOrderDto | EnhancedOrderDto,
  ): Promise<OrderResponseDto> {
    const draftOrder = await this.orderRepository.findById(draftOrderId);
    if (!draftOrder) {
      throw new OrderNotFoundException(draftOrderId);
    }

    if (draftOrder.tenantId !== tenantId) {
      throw new OrderTenantMismatchException(draftOrderId, tenantId);
    }

    if (draftOrder.status !== OrderStatus.Draft) {
      throw new InvalidOrderDataException(
        `Order ${draftOrderId} is not a draft order. Current status: ${draftOrder.status}`,
        'status',
      );
    }

    const collectionAddressId =
      completeOrderDto.collectionAddressId || draftOrder.collectionAddressId;

    const deliveryAddressId =
      completeOrderDto.deliveryAddressId || draftOrder.deliveryAddressId;

    let totalItems = 1;
    let totalWeight = 0;
    let totalVolume = 0;

    if (completeOrderDto.items && completeOrderDto.items.length > 0) {
      totalItems = completeOrderDto.items.reduce(
        (sum, item) => sum + (item.quantity || 1),
        0,
      );

      totalWeight = completeOrderDto.items.reduce(
        (sum, item) => sum + (item.totalWeight || 0) * (item.quantity || 1),
        0,
      );

      totalVolume = completeOrderDto.items.reduce((sum, item) => {
        if (item.length && item.width && item.height) {
          const itemVolume =
            item.length * item.width * item.height * (item.quantity || 1);
          return sum + itemVolume;
        }
        return sum;
      }, 0);
    }

    let calculatedDistance = 0;
    if (collectionAddressId && deliveryAddressId) {
      try {
        console.log(
          'Calculating distance between addresses for draft completion using Google Maps API...',
        );
        calculatedDistance = await calculateAddressDistanceById(
          this.addressService,
          collectionAddressId,
          deliveryAddressId,
          DistanceUnit.Kilometers,
        );

        if (calculatedDistance > 0) {
          console.log(
            `Calculated distance for draft completion: ${calculatedDistance} ${DistanceUnit.Kilometers}`,
          );
        } else {
          console.log(
            'Could not calculate distance between addresses for draft completion (no route found or missing coordinates)',
          );
        }
      } catch (error) {
        console.error(
          'Error calculating distance between addresses for draft completion:',
          error,
        );
      }
    }

    const queryRunner = this.connection.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      const updateData: Partial<Order> = {
        referenceNumber:
          completeOrderDto.referenceNumber || draftOrder.referenceNumber,

        collectionAddressId,
        collectionContactName:
          completeOrderDto.collectionContactName ||
          draftOrder.collectionContactName,
        collectionInstructions:
          completeOrderDto.collectionInstructions ||
          draftOrder.collectionInstructions,
        collectionSignatureRequired:
          completeOrderDto.collectionSignatureRequired ?? false,
        scheduledCollectionTime: completeOrderDto.scheduledCollectionTime,

        deliveryAddressId,
        deliveryContactName:
          completeOrderDto.deliveryContactName ||
          draftOrder.deliveryContactName,
        deliveryInstructions:
          completeOrderDto.deliveryInstructions ||
          draftOrder.deliveryInstructions,
        deliverySignatureRequired:
          completeOrderDto.deliverySignatureRequired ?? false,
        scheduledDeliveryTime: (completeOrderDto as any).scheduledDeliveryTime,

        totalItems,
        totalWeight,
        totalVolume,
        declaredValue: completeOrderDto.declaredValue,

        codAmount: completeOrderDto.codAmount,

        priceSetId: completeOrderDto.priceSetId,

        distance: calculatedDistance,

        description: completeOrderDto.description || draftOrder.description,
        comments: completeOrderDto.comments || draftOrder.comments,
        internalNotes: completeOrderDto.internalNotes,

        customFields: completeOrderDto.customFields || {},
        metadata: {
          ...draftOrder.metadata,
          ...completeOrderDto.metadata,
          completedFromDraft: true,
          completedAt: new Date().toISOString(),
          completedBy: userId,
        },

        updatedBy: userId,
      };

      const updatedOrder = await this.orderRepository.update(
        draftOrderId,
        tenantId,
        updateData,
      );

      if (!updatedOrder) {
        throw new OrderUpdateFailedException(
          draftOrderId,
          'Failed to complete draft order',
        );
      }

      await this.orderStatusHistoryRepository.create(
        draftOrderId,
        OrderStatus.Draft,
        updatedOrder.status,
        userId,
        'Draft order completed',
        'Order moved from draft to active status with complete information',
      );

      if (completeOrderDto.items && completeOrderDto.items.length > 0) {
        await this.orderItemRepository.createBulk(
          draftOrderId,
          completeOrderDto.items,
        );

        const totals =
          await this.orderItemRepository.calculateOrderTotals(draftOrderId);
        if (totals) {
          await this.orderRepository.update(draftOrderId, tenantId, {
            totalItems: totals.totalItems,
            totalWeight: totals.totalWeight,
            totalVolume: totals.totalVolume,
            declaredValue: totals.totalDeclaredValue,
          });
        }
      }

      if (completeOrderDto.scheduledCollectionTime) {
        await this.orderStopHistoryRepository.create(
          draftOrderId,
          OrderStopType.Collection,
          completeOrderDto.scheduledCollectionTime,
          userId,
        );
      }

      if ((completeOrderDto as any).scheduledDeliveryTime) {
        await this.orderStopHistoryRepository.create(
          draftOrderId,
          OrderStopType.Delivery,
          (completeOrderDto as any).scheduledDeliveryTime,
          userId,
        );
      }

      await queryRunner.commitTransaction();

      this.eventEmitter.emit('order.draft.completed', {
        orderId: draftOrderId,
        tenantId: updatedOrder.tenantId,
        previousStatus: OrderStatus.Draft,
        currentStatus: updatedOrder.status,
        completedBy: userId,
      });

      const finalOrder = await this.orderRepository.findById(draftOrderId);
      if (!finalOrder) {
        throw new OrderNotFoundException(draftOrderId);
      }

      return this.mapOrderToResponseDto(finalOrder);
    } catch (error) {
      await queryRunner.rollbackTransaction();
      if (
        error instanceof OrderNotFoundException ||
        error instanceof OrderTenantMismatchException ||
        error instanceof InvalidOrderDataException ||
        error instanceof OrderUpdateFailedException ||
        error instanceof OrderDeliveryTimeInvalidException
      ) {
        throw error;
      }
      throw new OrderUpdateFailedException(
        draftOrderId,
        `Failed to complete draft order: ${error.message}`,
      );
    } finally {
      await queryRunner.release();
    }
  }
  private mapOrderToResponseDto(order: Order): OrderResponseDto {
    const dto = new OrderResponseDto();
    dto.id = order.id;
    dto.tenantId = order.tenantId;
    dto.trackingNumber = order.trackingNumber;
    dto.referenceNumber = order.referenceNumber;
    dto.status = order.status;
    dto.customerId = order.customerId;
    dto.customerName = order.customerName;
    dto.customerEmail = order.customerEmail;
    dto.customerPhoneNumber = order.customerPhoneNumber;
    dto.companyName = order.companyName;
    dto.customerContactName = order.customerContactName;
    dto.requestedById = order.requestedById;
    dto.requestedByName = order.requestedByName;
    dto.collectionAddressId = order.collectionAddressId;
    dto.collectionAddressLine1 = order.collectionAddressLine1;
    dto.collectionAddressLine2 = order.collectionAddressLine2;
    dto.collectionPostalCode = order.collectionPostalCode;
    dto.collectionCity = order.collectionCity;
    dto.collectionProvince = order.collectionProvince;
    dto.collectionCountry = order.collectionCountry;

    dto.deliveryAddressLine1 = order.deliveryAddressLine1;
    dto.deliveryAddressLine2 = order.deliveryAddressLine2;
    dto.deliveryPostalCode = order.deliveryPostalCode;
    dto.deliveryCity = order.deliveryCity;
    dto.deliveryProvince = order.deliveryProvince;
    dto.deliveryCountry = order.deliveryCountry;
    dto.collectionAddress = order.collectionAddress;
    dto.collectionZoneName = order.collectionZoneName;
    dto.collectionZoneId = order.collectionZoneId;
    dto.collectionContactName = order.collectionContactName;
    dto.collectionCompanyName = order.collectionCompanyName;
    dto.collectionEmail = order.collectionEmail;
    dto.collectionPhone = order.collectionPhone;
    dto.collectionPhoneExtension = order.collectionPhoneExtension;
    dto.collectionInstructions = order.collectionInstructions;
    dto.collectionSignatureRequired = order.collectionSignatureRequired;
    dto.scheduledCollectionTime = order.scheduledCollectionTime;
    dto.actualCollectionTime = order.actualCollectionTime;
    dto.deliveryAddressId = order.deliveryAddressId;
    dto.deliveryZoneName = order.deliveryZoneName;
    dto.deliveryAddress = order.deliveryAddress;
    dto.deliveryCompanyName = order.deliveryCompanyName;
    dto.deliveryZoneId = order.deliveryZoneId;
    dto.deliveryContactName = order.deliveryContactName;
    dto.deliveryInstructions = order.deliveryInstructions;
    dto.deliverySignatureRequired = order.deliverySignatureRequired;
    dto.deliveryEmail = order.deliveryEmail;
    dto.deliveryPhone = order.deliveryPhone;
    dto.deliveryPhoneExtension = order.deliveryPhoneExtension;
    dto.scheduledDeliveryTime = order.scheduledDeliveryTime;
    dto.actualDeliveryTime = order.actualDeliveryTime;
    dto.packageTemplateId = order.packageTemplateId;
    dto.totalItems = order.totalItems;
    dto.totalWeight = order.totalWeight;
    dto.totalVolume = order.totalVolume;
    dto.declaredValue = order.declaredValue;
    dto.assignedDriverId = order.assignedDriverId;
    dto.assignedDriverName = order.assignedDriverName;
    dto.assignedVehicleId = order.assignedVehicleId;
    dto.assignedVehicleName = order.assignedVehicleName;
    dto.basePrice = order.basePrice;
    dto.isLocked = order.isLocked;
    dto.isCod = order.isCod;
    dto.codCollected = order.codCollected;
    dto.assignedVehicleDescription = order.assignedVehicleDescription;
    dto.optionsPrice = order.optionsPrice;
    dto.miscAdjustment = order.miscAdjustment;
    dto.customerAdjustment = order.customerAdjustment;
    dto.totalPrice = order.totalPrice;
    dto.billingStatus = order.billingStatus;
    dto.paymentStatus = order.paymentStatus;
    dto.distance = order.distance;
    dto.distanceUnit = order.distanceUnit;
    dto.estimatedDuration = order.estimatedDuration;
    dto.description = order.description;
    dto.priceSetId = order.priceSetId;
    dto.priceSet = order.priceSet;
    dto.serviceLevel = order.priceSetInternalName;
    dto.comments = order.comments;
    dto.internalNotes = order.internalNotes;
    dto.codAmount = order.codAmount;
    dto.codCollected = order.codCollected;
    dto.invoiceNumber = order.invoiceNumber;
    dto.customPricingSummary = order.customPricingSummary;
    dto.createdAt = order.createdAt;
    dto.updatedAt = order.updatedAt;

    return dto;
  }

  private mapOrderToDetailResponseDto(order: Order): OrderDetailResponseDto {
    const basicOrderResponse = this.mapOrderToResponseDto(order);

    const detailResponseDto = new OrderDetailResponseDto();
    Object.assign(detailResponseDto, basicOrderResponse);

    detailResponseDto.statusHistory = [];
    detailResponseDto.assignmentHistory = [];
    detailResponseDto.stopHistory = [];

    return detailResponseDto;
  }

  async getCustomPricingSummary(
    tenantId: string,
    orderId: string,
  ): Promise<{
    data: Array<{ name: string; amount: number }>;
  }> {
    const pricingSummary = await this.orderRepository.getCustomPricingSummary(
      orderId,
      tenantId,
    );

    if (!pricingSummary) {
      return { data: [] }; // Return empty array if no pricing summary found
    }

    // Extract from main modifiers (pricingSummary.modifiers)
    const originalModifiers =
      pricingSummary.modifiers?.map((mod) => ({
        customModifierName: mod.name,
        customModifierAmount: mod.amount,
        createdAt: mod.createdAt,
      })) || [];

    // Extract from customModifiers
    const customModifiers =
      pricingSummary.customModifiers?.map((mod) => ({
        customModifierName: mod.name,
        customModifierAmount: mod.amount,
        createdAt: mod.createdAt,
      })) || [];

    // Combine and return
    const data = [...originalModifiers, ...customModifiers];

    return { data };
  }

  public async getOrderAttachments(orderId: string): Promise<OrderAttachments> {
    const attachedImages =
      await this.fileUploadService.getFilesByEntityTypeAndId('order', orderId);

    const attachments: any = {
      pickupSignature: null,
      deliverySignature: null,
      images: [],
    };

    for (const image of attachedImages) {
      if (image.type === FILE_UPLOAD_CONSTANTS.COLLECTION_SIGNATURE) {
        attachments.pickupSignature = image.url;
      } else if (image.type === FILE_UPLOAD_CONSTANTS.DELIVERY_SIGNATURE) {
        attachments.deliverySignature = image.url;
      } else if (image.mimeType.startsWith('image/')) {
        attachments.images.push(image?.url);
      }
    }

    return attachments;
  }
}
