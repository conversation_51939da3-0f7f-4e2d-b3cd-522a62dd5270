/**
 * Supported bill types
 */
export enum OrderBills {
  WAY_BILL = 'wayBill',
  BILL_OF_LADING = 'billOfLading',
  SHIPPING_LABEL = 'shippingLabel',
}

/**
 * Template variables that can be used in the bill of lading template
 */
export enum TEMPLATE_VARIABLE { //TODO: use handBar lib
  // tenant Info
  TENANT_NAME = 'TEMPLATE_VARIABLE.TENANT_NAME',
  TENANT_PHONE_NUMBER = 'TEMPLATE_VARIABLE.TENANT_PHONE_NUMBER',
  TENANT_WEBSITE = 'TEMPLATE_VARIABLE.TENANT_WEBSITE',
  TENANT_LOGO = 'TEMPLATE_VARIABLE.TENANT_LOGO',

  // Basic order information
  TRACKING_NUMBER = 'TEMPLATE_VARIABLE.TRACKING_NUMBER',
  SUBMITTED_DATE = 'TEMPLATE_VARIABLE.SUBMITTED_DATE',

  // Collection location information
  COLLECTION_ADDRESS_LINE_1 = 'TEMPLATE_VARIABLE.COLLECTION_ADDRESS_LINE_1',
  COLLECTION_ADDRESS_LINE_2 = 'TEMPLATE_VARIABLE.COLLECTION_ADDRESS_LINE_2',
  COLLECTION_CITY = 'TEMPLATE_VARIABLE.COLLECTION_CITY',
  COLLECTION_POSTAL_CODE = 'TEMPLATE_VARIABLE.COLLECTION_POSTAL_CODE',
  COLLECTION_COMPANY_NAME = 'TEMPLATE_VARIABLE.COLLECTION_COMPANY_NAME',
  COLLECTION_CONTACT_NAME = 'TEMPLATE_VARIABLE.COLLECTION_CONTACT_NAME',
  COLLECTION_EMAIL = 'TEMPLATE_VARIABLE.COLLECTION_EMAIL',
  COLLECTION_PHONE_NUMBER = 'TEMPLATE_VARIABLE.COLLECTION_PHONE_NUMBER',

  // Delivery location information
  DELIVERY_ADDRESS_LINE_1 = 'TEMPLATE_VARIABLE.DELIVERY_ADDRESS_LINE_1',
  DELIVERY_ADDRESS_LINE_2 = 'TEMPLATE_VARIABLE.DELIVERY_ADDRESS_LINE_2',
  DELIVERY_CITY = 'TEMPLATE_VARIABLE.DELIVERY_CITY',
  DELIVERY_POSTAL_CODE = 'TEMPLATE_VARIABLE.DELIVERY_POSTAL_CODE',
  DELIVERY_COMPANY_NAME = 'TEMPLATE_VARIABLE.DELIVERY_COMPANY_NAME',
  DELIVERY_CONTACT_NAME = 'TEMPLATE_VARIABLE.DELIVERY_CONTACT_NAME',
  DELIVERY_EMAIL = 'TEMPLATE_VARIABLE.DELIVERY_EMAIL',
  DELIVERY_PHONE_NUMBER = 'TEMPLATE_VARIABLE.DELIVERY_PHONE_NUMBER',

  // Legacy variables (for backward compatibility)
  SENDER_NAME = 'TEMPLATE_VARIABLE.SENDER_NAME',
  SENDER_ADDRESS = 'TEMPLATE_VARIABLE.SENDER_ADDRESS',
  RECEIVER_NAME = 'TEMPLATE_VARIABLE.RECEIVER_NAME',
  RECEIVER_ADDRESS = 'TEMPLATE_VARIABLE.RECEIVER_ADDRESS',

  // Service details
  SERVICE_LEVEL = 'TEMPLATE_VARIABLE.SERVICE_LEVEL',
  EXPECTED_COLLECTION_TIME = 'TEMPLATE_VARIABLE.EXPECTED_COLLECTION_TIME',
  EXPECTED_DELIVERY_TIME = 'TEMPLATE_VARIABLE.EXPECTED_DELIVERY_TIME',
  ACTUAL_COLLECTION_TIME = 'TEMPLATE_VARIABLE.ACTUAL_COLLECTION_TIME',
  ACTUAL_DELIVERY_TIME = 'TEMPLATE_VARIABLE.ACTUAL_DELIVERY_TIME',

  // Package information
  DESCRIPTION = 'TEMPLATE_VARIABLE.DESCRIPTION',
  QUANTITY = 'TEMPLATE_VARIABLE.QUANTITY',
  WEIGHT = 'TEMPLATE_VARIABLE.WEIGHT',
  DIMENSIONS = 'TEMPLATE_VARIABLE.DIMENSIONS',
  DECLARED_VALUE = 'TEMPLATE_VARIABLE.DECLARED_VALUE',
  DELIVERY_INSTRUCTIONS = 'TEMPLATE_VARIABLE.DELIVERY_INSTRUCTIONS',
  ORDER_ITEMS = 'TEMPLATE_VARIABLE.ORDER_ITEMS',

  // Reference information
  REF_NUMBER = 'TEMPLATE_VARIABLE.REF_NUMBER',

  // Invoice information
  INVOICE_NUMBER = 'TEMPLATE_VARIABLE.INVOICE_NUMBER',
  INVOICE_DATE = 'TEMPLATE_VARIABLE.INVOICE_DATE',
  INVOICE_TOTAL_AMOUNT = 'TEMPLATE_VARIABLE.INVOICE_TOTAL_AMOUNT',
  INVOICE_ORDERS = 'TEMPLATE_VARIABLE.INVOICE_ORDERS',
  INVOICE_ORDER_DETAILS = 'TEMPLATE_VARIABLE.INVOICE_ORDER_DETAILS',

  // Special variables
  BARCODE = 'TEMPLATE_VARIABLE.BARCODE',
  BARCODE_WITH_TEXT = 'TEMPLATE_VARIABLE.BARCODE_WITH_TEXT',
  INVOICE_SHADDER_IMAGE = 'TEMPLATE_VARIABLE.INVOICE_SHADDER_IMAGE',

  //other
  DRIVER_NAME = 'TEMPLATE_VARIABLE.DRIVER',
  VEHICLE_NUMBER = 'TEMPLATE_VARIABLE.VEHICLE_NUMBER',
}
