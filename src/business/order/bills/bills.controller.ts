import { <PERSON>, Get, UseGuards, <PERSON><PERSON>, <PERSON><PERSON>, Param } from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiTags,
  ApiOperation,
  ApiOkResponse,
  ApiCookieAuth,
  ApiProduces,
} from '@nestjs/swagger';
import { BillsService } from './bills.service';
import { Response } from 'express';
import { JwtPayload } from '../../../core/auth/domain/auth.types';
import { CurrentUser } from '../../../core/auth/decorators/current-user.decorator';
import { OrderBills } from './constants/template-variables.constant';
import { ContactsService } from '../../user/contacts/contacts.service';
import { ContactPermissionGuard } from '../../user/contacts/guards/contact-permission.guard';
import { JwtContactAuthGuard } from '../../../core/auth/guards/jwt-contact-auth.guard';

@ApiTags('Business - Order - Bills')
@ApiBearerAuth()
@Controller({
  path: '/orders',
  version: '1',
})
@UseGuards(JwtContactAuthGuard)
@ApiCookieAuth('contact_session_token')
export class BillsController {
  constructor(
    private readonly billsService: BillsService,
    private readonly contactsService: ContactsService,
  ) {}

  @Get(':orderId/bills/:billType')
  @ApiOperation({ summary: 'Generate and return bill PDF' })
  @ApiOkResponse({ description: 'PDF binary stream' })
  @Header('Content-Type', 'application/pdf')
  @Header('Content-Disposition', 'inline; filename="bill-of-lading.pdf"')
  @UseGuards(JwtContactAuthGuard, ContactPermissionGuard)
  @ApiProduces('application/pdf')
  async getBill(
    @CurrentUser() contactData: JwtPayload,
    @Param('orderId') orderId: string,
    @Param('billType') billType: OrderBills,
    @Res() res: Response,
  ): Promise<any> {
    const contact = await this.contactsService.findById(contactData.sub);

    const tenantId = contact.tenantId;

    if (!tenantId) {
      throw new Error('Contact does not have a tenant ID');
    }

    const pdfBuffer = await this.billsService.getBill(
      orderId,
      billType,
      tenantId,
    );
    res.set({
      'Content-Type': 'application/pdf',
      'Content-Disposition': `inline; filename="${billType}.pdf"`,
      'Content-Length': pdfBuffer.length,
    });
    res.send(pdfBuffer);
  }

  @Get('bills/invoice')
  @ApiOperation({ summary: 'Generate and return bill PDF' })
  @ApiOkResponse({ description: 'PDF binary stream' })
  @Header('Content-Type', 'application/pdf')
  @Header('Content-Disposition', 'inline; filename="invoice.pdf"')
  @ApiProduces('application/pdf')
  async printInvoice(
    @CurrentUser() contactData: JwtPayload,
    // @Param('orderId') invoiceId: string,
    @Res() res: Response,
  ): Promise<any> {
    const contact = await this.contactsService.findById(contactData.sub);

    const tenantId = contact.tenantId;

    if (!tenantId) {
      throw new Error('Contact does not have a tenant ID');
    }

    const pdfBuffer = await this.billsService.printInvoice(
      'invoiceId',
      tenantId,
    );
    res.set({
      'Content-Type': 'application/pdf',
      'Content-Disposition': `inline; filename="${'invoice'}.pdf"`,
      'Content-Length': pdfBuffer.length,
    });
    res.send(pdfBuffer);
  }
}
