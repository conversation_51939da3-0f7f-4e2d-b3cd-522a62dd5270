import JsBarcode from 'jsbarcode';
import { createCanvas } from 'canvas';
import * as fs from 'fs';
import * as path from 'path';
import { OrderDetailResponseDto } from '../../orders/dto/order-detail-response.dto';
import { Tenant } from '../../../user/tenants/domain/tenant';
import { formatDateTime } from '../../../pricing/utils/date.utils';
import { DEFAULT_DATETIME_FORMAT_12H } from '../../../pricing/constants/timezone.constants';
import { TEMPLATE_VARIABLE } from '../constants/template-variables.constant';
import { OrderItemResponseDto } from '../../orders/dto/order-item-response.dto';

/**
 * Interface for barcode generation options
 */
export interface BarcodeOptions {
  width?: number;
  height?: number;
  format?: 'CODE128' | 'CODE39' | 'EAN13' | 'EAN8' | 'UPC' | 'ITF14';
  displayValue?: boolean;
  fontSize?: number;
  textAlign?: 'left' | 'center' | 'right';
  textPosition?: 'bottom' | 'top';
  background?: string;
  lineColor?: string;
  margin?: number;
}

export interface TemplateParserPayload {
  order?: OrderDetailResponseDto;
  tenant?: Tenant;
  invoice?: { orders: OrderDetailResponseDto[] };
}

/**
 * Template parser utility for processing template variables in bill of lading templates
 */
export class TemplateParser {
  private static readonly DEFAULT_BARCODE_OPTIONS: BarcodeOptions = {
    width: 2,
    height: 70,
    format: 'CODE128',
    displayValue: true,
    fontSize: 12,
    textAlign: 'center',
    textPosition: 'bottom',
    background: '#ffffff',
    lineColor: '#000000',
    margin: 10,
  };

  /**
   * Parse template string and replace template variables with actual data
   * @param template - The template string containing TEMPLATE_VARIABLE placeholders
   * @param data - The data to replace template variables with
   * @param tenant - The tenant data for tenant-specific variables
   * @returns Parsed template string with variables replaced
   */
  public static parseTemplate(
    template: string,
    data: TemplateParserPayload,
  ): string {
    let parsedTemplate = template;

    Object.values(TEMPLATE_VARIABLE).forEach((variable) => {
      // Build regex for exact match of the placeholder
      const regex = new RegExp(`\\b${this.escapeRegExp(variable)}\\b`, 'g');

      // Get replacement value (fallback to empty string if undefined/null)
      const replacement = this.getVariableValue(variable, data) ?? '';

      // Use a function to prevent special characters ($, \) in the replacement
      parsedTemplate = parsedTemplate.replace(regex, () => String(replacement));
    });

    return parsedTemplate;
  }

  /**
   * Get the value for a specific template variable
   * @param variable - The template variable
   * @param data - The data object
   * @param tenant - The tenant data for tenant-specific variables
   * @returns The value to replace the variable with
   */
  private static getVariableValue(
    variable: TEMPLATE_VARIABLE,
    data: TemplateParserPayload,
  ): string {
    const combinedData = { ...data.order, ...data.tenant, ...data.invoice };

    switch (variable) {
      case TEMPLATE_VARIABLE.TRACKING_NUMBER:
        return combinedData.trackingNumber || '';

      case TEMPLATE_VARIABLE.SUBMITTED_DATE:
        return combinedData.createdAt
          ? formatDateTime(combinedData.createdAt, DEFAULT_DATETIME_FORMAT_12H)
          : 'NA';

      // Collection location variables
      case TEMPLATE_VARIABLE.COLLECTION_ADDRESS_LINE_1:
        return combinedData.collectionAddressLine1 || '';

      case TEMPLATE_VARIABLE.COLLECTION_ADDRESS_LINE_2:
        return combinedData.collectionAddressLine2 || '';

      case TEMPLATE_VARIABLE.COLLECTION_CITY:
        return combinedData.collectionCity || '';

      case TEMPLATE_VARIABLE.COLLECTION_POSTAL_CODE:
        return combinedData.collectionPostalCode || '';

      case TEMPLATE_VARIABLE.COLLECTION_COMPANY_NAME:
        return combinedData.collectionCompanyName || '';

      case TEMPLATE_VARIABLE.COLLECTION_CONTACT_NAME:
        return combinedData.collectionContactName || '';

      case TEMPLATE_VARIABLE.COLLECTION_EMAIL:
        return combinedData.collectionEmail || '';

      case TEMPLATE_VARIABLE.COLLECTION_PHONE_NUMBER:
        return combinedData.collectionPhone || '';

      // Delivery location variables
      case TEMPLATE_VARIABLE.DELIVERY_ADDRESS_LINE_1:
        return combinedData.deliveryAddressLine1 || '';

      case TEMPLATE_VARIABLE.DELIVERY_ADDRESS_LINE_2:
        return combinedData.deliveryAddressLine2 || '';

      case TEMPLATE_VARIABLE.DELIVERY_CITY:
        return combinedData.deliveryCity || '';

      case TEMPLATE_VARIABLE.DELIVERY_POSTAL_CODE:
        return combinedData.deliveryPostalCode || '';

      case TEMPLATE_VARIABLE.DELIVERY_COMPANY_NAME:
        return combinedData.deliveryCompanyName || '';

      case TEMPLATE_VARIABLE.DELIVERY_CONTACT_NAME:
        return combinedData.deliveryContactName || '';

      case TEMPLATE_VARIABLE.DELIVERY_EMAIL:
        return combinedData.deliveryEmail || '';

      case TEMPLATE_VARIABLE.DELIVERY_PHONE_NUMBER:
        return combinedData.deliveryPhone || '';

      // Legacy variables (for backward compatibility)
      case TEMPLATE_VARIABLE.SENDER_NAME:
        return `${combinedData.collectionCompanyName || ''}, ${combinedData.collectionContactName || ''}`;

      case TEMPLATE_VARIABLE.SENDER_ADDRESS:
        return (
          `${combinedData.collectionAddressLine1}, ${combinedData.collectionAddressLine2}, ${combinedData.collectionCity}, ${combinedData.collectionPostalCode}` ||
          ''
        );

      case TEMPLATE_VARIABLE.RECEIVER_NAME:
        return `${combinedData.deliveryCompanyName || ''}, ${combinedData.deliveryContactName || ''}`;

      case TEMPLATE_VARIABLE.RECEIVER_ADDRESS:
        return (
          `${combinedData.deliveryAddressLine1}, ${combinedData.deliveryAddressLine2}, ${combinedData.deliveryCity}, ${combinedData.deliveryPostalCode}` ||
          ''
        );

      case TEMPLATE_VARIABLE.SERVICE_LEVEL:
        return combinedData.serviceLevel || '';

      case TEMPLATE_VARIABLE.EXPECTED_COLLECTION_TIME:
        return combinedData.actualCollectionTime
          ? formatDateTime(
              combinedData.actualCollectionTime,
              DEFAULT_DATETIME_FORMAT_12H,
            )
          : 'NA';

      case TEMPLATE_VARIABLE.EXPECTED_DELIVERY_TIME:
        return combinedData.scheduledDeliveryTime
          ? formatDateTime(
              combinedData.scheduledDeliveryTime,
              DEFAULT_DATETIME_FORMAT_12H,
            )
          : 'NA';

      case TEMPLATE_VARIABLE.ACTUAL_COLLECTION_TIME:
        return combinedData.actualCollectionTime
          ? formatDateTime(
              combinedData.actualCollectionTime,
              DEFAULT_DATETIME_FORMAT_12H,
            )
          : 'NA';

      case TEMPLATE_VARIABLE.ACTUAL_DELIVERY_TIME:
        return combinedData.actualDeliveryTime
          ? formatDateTime(
              combinedData.actualDeliveryTime,
              DEFAULT_DATETIME_FORMAT_12H,
            )
          : 'NA';

      case TEMPLATE_VARIABLE.DESCRIPTION:
        return combinedData.description || 'NA';

      case TEMPLATE_VARIABLE.DELIVERY_INSTRUCTIONS:
        return combinedData.internalNotes || 'NA';

      case TEMPLATE_VARIABLE.QUANTITY:
        return String(combinedData.totalItems) || '';

      case TEMPLATE_VARIABLE.WEIGHT:
        return String(combinedData.totalWeight) || '';

      case TEMPLATE_VARIABLE.DIMENSIONS:
        return this.generateOrderItems(combinedData.items || []);

      case TEMPLATE_VARIABLE.DECLARED_VALUE:
        return String(combinedData.declaredValue) || '';

      case TEMPLATE_VARIABLE.REF_NUMBER:
        return combinedData.referenceNumber || 'NA';

      case TEMPLATE_VARIABLE.DRIVER_NAME:
        return combinedData.assignedDriverName || 'NA';

      case TEMPLATE_VARIABLE.BARCODE:
        return this.generateBarcodeDataUrl(combinedData.trackingNumber || '', {
          displayValue: false,
        });

      case TEMPLATE_VARIABLE.BARCODE_WITH_TEXT:
        return this.generateBarcodeDataUrl(combinedData.trackingNumber || '', {
          displayValue: true,
        });

      case TEMPLATE_VARIABLE.INVOICE_NUMBER:
        return combinedData.invoiceNumber || 'NA';

      case TEMPLATE_VARIABLE.INVOICE_DATE:
        return combinedData.createdAt
          ? formatDateTime(combinedData.createdAt, DEFAULT_DATETIME_FORMAT_12H)
          : formatDateTime(new Date(), DEFAULT_DATETIME_FORMAT_12H);

      case TEMPLATE_VARIABLE.INVOICE_TOTAL_AMOUNT:
        return combinedData.totalPrice
          ? String(combinedData.totalPrice)
          : '$0.00';

      case TEMPLATE_VARIABLE.INVOICE_ORDERS:
        return this.generateInvoiceOrdersTable(combinedData.orders || []);

      case TEMPLATE_VARIABLE.INVOICE_ORDERS_PAGE_ONE:
        return this.generateInvoiceOrdersTablePageOne(
          combinedData.orders || [],
        );

      case TEMPLATE_VARIABLE.INVOICE_MORE_ORDERS_INDICATOR:
        return this.generateMoreOrdersIndicator(combinedData.orders || []);

      case TEMPLATE_VARIABLE.INVOICE_ORDERS_REMAINING:
        return this.generateInvoiceOrdersRemaining(combinedData.orders || []);

      case TEMPLATE_VARIABLE.INVOICE_SHADDER_IMAGE:
        return this.getInvoiceShadderImageDataUrl();

      case TEMPLATE_VARIABLE.INVOICE_ORDER_DETAILS:
        return this.generateInvoiceOrderDetails(combinedData.orders || []);

      case TEMPLATE_VARIABLE.TENANT_LOGO:
        const tenantLogo = combinedData?.logoUrl?.trim();
        if (tenantLogo && tenantLogo !== '') {
          return tenantLogo;
        }

        return this.getLumigoLogoDataUrl();

      default:
        return '';
    }
  }

  /**
   * Generate a barcode as a data URL
   * @param value - The value to encode in the barcode
   * @param options - Barcode generation options
   * @returns Data URL string for the barcode image
   */
  private static generateBarcodeDataUrl(
    value: string,
    options: Partial<BarcodeOptions> = {},
  ): string {
    if (!value) {
      return '';
    }

    const barcodeOptions = { ...this.DEFAULT_BARCODE_OPTIONS, ...options };

    try {
      // Create a canvas for barcode generation
      const canvas = createCanvas(300, barcodeOptions.height || 70);

      // Generate barcode using JsBarcode
      JsBarcode(canvas, value, {
        format: barcodeOptions.format,
        width: barcodeOptions.width,
        height: barcodeOptions.height,
        displayValue: barcodeOptions.displayValue,
        fontSize: 16,
        textAlign: barcodeOptions.textAlign,
        textPosition: barcodeOptions.textPosition,
        background: barcodeOptions.background,
        lineColor: barcodeOptions.lineColor,
        margin: barcodeOptions.margin,
      });

      // Convert canvas to data URL
      return canvas.toDataURL('image/png');
    } catch (error) {
      console.error('Error generating barcode:', error);
      // Fallback to SVG barcode if canvas fails
      return this.createFallbackBarcodeSvg(value, barcodeOptions);
    }
  }

  /**
   * Create a fallback SVG barcode when canvas generation fails
   */
  private static createFallbackBarcodeSvg(
    value: string,
    options: BarcodeOptions,
  ): string {
    // This is a placeholder implementation
    // In production, you would use a proper barcode generation library
    const width = 200;
    const height = options.height || 70;

    // Create a simple SVG barcode pattern
    const svg = `
      <svg width="${width}" height="${height}" xmlns="http://www.w3.org/2000/svg">
        <rect width="${width}" height="${height}" fill="${options.background}"/>
        ${this.generateBarcodePattern(value, width, height - 20)}
        ${options.displayValue ? `<text x="${width / 2}" y="${height - 5}" text-anchor="middle" font-family="monospace" font-size="12" fill="${options.lineColor}">${value}</text>` : ''}
      </svg>
    `;

    // Convert SVG to data URL
    const base64 = Buffer.from(svg).toString('base64');
    return `data:image/svg+xml;base64,${base64}`;
  }

  /**
   * Generate a simple barcode pattern based on the value
   */
  private static generateBarcodePattern(
    value: string,
    width: number,
    height: number,
  ): string {
    const bars: string[] = [];
    const barWidth = width / (value.length * 8); // Approximate bar width

    for (let i = 0; i < value.length; i++) {
      const charCode = value.charCodeAt(i);
      const pattern = charCode.toString(2).padStart(8, '0'); // Convert to binary

      for (let j = 0; j < pattern.length; j++) {
        const x = (i * 8 + j) * barWidth;
        if (pattern[j] === '1') {
          bars.push(
            `<rect x="${x}" y="0" width="${barWidth}" height="${height}" fill="#000000"/>`,
          );
        }
      }
    }

    return bars.join('');
  }

  /**
   * Get Lumigo logo as a data URL for reliable fallback
   * @returns Data URL string for the Lumigo logo SVG
   */
  private static getLumigoLogoDataUrl(): string {
    const lumigoSvg = `<svg width="154" height="40" viewBox="0 0 154 40" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M5.30345 2.55702H0V31.1167H20.2363V26.6288H5.30345V2.55702Z" fill="#EF5C26"/>
<path d="M40.906 20.8535C40.906 22.2574 40.6683 23.4341 40.1907 24.3872C39.7141 25.3402 39.0396 26.0544 38.1658 26.5309C37.2919 27.0074 36.2725 27.2451 35.1086 27.2451C33.5462 27.2451 32.3287 26.7756 31.4549 25.8354C30.5811 24.8963 30.1442 23.4866 30.1442 21.6061V10.1301H25.1413V22.2411C25.1413 24.3592 25.5316 26.1127 26.3122 27.5026C27.0928 28.8925 28.1717 29.9248 29.5488 30.5994C30.9248 31.274 32.5128 31.6118 34.314 31.6118C35.9556 31.6118 37.4644 31.2414 38.8403 30.5004C39.7538 30.0087 40.5216 29.3819 41.1437 28.6211V31.3346H45.8693V10.1324H40.906V20.8535Z" fill="#EF5C26"/>
<path d="M83.6518 10.8466C82.3411 10.1848 80.8393 9.85392 79.1453 9.85392C77.0539 9.85392 75.2143 10.3444 73.6263 11.3231C72.6546 11.9219 71.8565 12.6315 71.232 13.4482C70.7252 12.5767 70.0681 11.8672 69.2584 11.3231C67.802 10.3444 66.0812 9.85392 64.0959 9.85392C62.3226 9.85392 60.7533 10.2244 59.3901 10.9654C58.5047 11.4466 57.7578 12.0559 57.1462 12.7934V10.1312H52.4206V31.3357H57.3442V20.575C57.3442 19.1722 57.5761 18.0013 58.0386 17.0611C58.5012 16.1221 59.1501 15.4137 59.9843 14.9372C60.8185 14.4607 61.7774 14.2218 62.8632 14.2218C64.3976 14.2218 65.5837 14.6855 66.4167 15.6118C67.2509 16.5392 67.668 17.9419 67.668 19.8212V31.3369H72.5917V20.5762C72.5917 19.1734 72.8235 18.0025 73.2861 17.0623C73.7486 16.1232 74.3975 15.4149 75.2317 14.9383C76.0659 14.4618 77.0248 14.223 78.1107 14.223C79.6451 14.223 80.8311 14.6867 81.6642 15.6129C82.4984 16.5403 82.9155 17.9431 82.9155 19.8224V31.338H87.8391V19.1874C87.8391 17.0436 87.4686 15.2832 86.7276 13.9061C85.9866 12.5301 84.9602 11.5107 83.6506 10.8489L83.6518 10.8466Z" fill="#EF5C26"/>
<path d="M98.9796 1.49562C98.3842 0.953858 97.6223 0.681229 96.696 0.681229C95.7698 0.681229 95.009 0.972499 94.4125 1.55504C93.8171 2.13758 93.5189 2.82615 93.5189 3.61957C93.5189 4.46658 93.8171 5.18194 94.4125 5.76332C95.0078 6.34586 95.7686 6.63713 96.696 6.63713C97.6234 6.63713 98.4169 6.33887 98.9994 5.74351C99.5819 5.14815 99.8732 4.42697 99.8732 3.57995C99.8732 2.73294 99.575 2.03855 98.9796 1.49562Z" fill="#EF5C26"/>
<path d="M99.1578 10.1324H94.2342V31.3369H99.1578V10.1324Z" fill="#EF5C26"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M147.476 9.8855L150.737 4.06008H150.739C151.283 5.03205 151.827 6.00402 152.371 6.97599C152.914 7.94583 153.457 8.91566 154 9.8855C153.855 9.70897 152.594 8.20759 150.547 8.3021C148.722 8.3859 147.647 9.67331 147.476 9.8855ZM119.509 10.9457C120.467 11.4304 121.295 12.0875 121.99 12.917H121.991V10.1325H126.717V28.1202C126.717 31.9591 125.737 34.7845 123.779 36.5985C121.819 38.4114 118.987 39.319 115.28 39.319C113.295 39.319 111.382 39.0603 109.542 38.5442C107.702 38.0281 106.173 37.2661 104.955 36.2606L107.18 32.5673C108.106 33.3351 109.27 33.9573 110.674 34.4338C112.076 34.9103 113.533 35.148 115.042 35.148C117.345 35.148 119.039 34.612 120.125 33.5402C121.21 32.4683 121.752 30.8465 121.752 28.6759V27.5551C121.108 28.2612 120.36 28.8332 119.509 29.2713C118.065 30.0123 116.444 30.3828 114.644 30.3828C112.605 30.3828 110.78 29.9529 109.165 29.0919C107.55 28.232 106.272 27.0343 105.333 25.4988C104.393 23.9643 103.923 22.2027 103.923 20.1382C103.923 18.0737 104.393 16.2667 105.333 14.7183C106.272 13.1699 107.55 11.9722 109.165 11.1251C110.779 10.2781 112.605 9.85404 114.644 9.85404C116.444 9.85404 118.065 10.2175 119.509 10.9457ZM118.734 25.3997C119.714 24.8836 120.474 24.1694 121.017 23.256C121.56 22.3437 121.832 21.3569 121.832 20.1394C121.832 18.9219 121.559 17.8628 121.017 16.9622C120.474 16.0628 119.712 15.3614 118.734 14.8581C117.754 14.3547 116.642 14.1031 115.398 14.1031C114.154 14.1031 113.035 14.3559 112.043 14.8581C111.05 15.3614 110.281 16.0628 109.739 16.9622C109.196 17.8628 108.925 18.9743 108.925 20.1394C108.925 21.3045 109.196 22.3426 109.739 23.256C110.282 24.1682 111.05 24.8836 112.043 25.3997C113.035 25.917 114.154 26.1745 115.398 26.1745C116.642 26.1745 117.754 25.9159 118.734 25.3997ZM150.021 14.1264C149.197 16.2759 147.757 18.217 145.746 19.6582V19.657C141.078 23.0043 134.847 22.579 130.684 18.9813C131.527 13.8211 136.006 9.88197 141.406 9.88197C144.914 9.88197 148.034 11.5457 150.021 14.1264ZM151.25 16.1455L151.25 16.1471L151.249 16.1455H151.25ZM151.25 16.1471C150.857 17.944 150.021 19.3145 150.021 19.3145C151.589 15.4247 150.754 11.3522 150.738 11.2708L150.737 11.2696C150.871 16.0255 148.717 20.7615 144.558 23.7418C140.737 26.4798 136.057 27.1415 131.862 25.9473C132.056 26.3038 132.271 26.6487 132.503 26.9796C133.083 27.8079 133.99 28.3555 134.995 28.4767C134.997 28.4767 134.999 28.4769 135.001 28.4772C135.003 28.4774 135.005 28.4777 135.006 28.4781C135.008 28.4784 135.009 28.4787 135.011 28.479C137.289 28.7493 139.456 28.0235 141.074 26.644C139.642 28.3043 137.66 29.453 135.453 29.8399C137.201 30.9863 139.3 31.6434 141.554 31.6131C147.602 31.5315 152.391 26.5718 152.268 20.5239C152.237 18.961 151.875 17.4807 151.25 16.1471Z" fill="#2D3484"/>
</svg>`;

    // Convert SVG to base64 data URL
    const base64 = Buffer.from(lumigoSvg).toString('base64');
    return `data:image/svg+xml;base64,${base64}`;
  }

  /**
   * Get invoice shadder image as a data URL
   * @returns Data URL string for the invoice shadder image
   */
  private static getInvoiceShadderImageDataUrl(): string {
    try {
      // Get the path to the image file
      const imagePath = path.join(
        process.cwd(),
        'src',
        'assets',
        'InvoiceTemplateShadder.png',
      );

      // Check if file exists
      if (fs.existsSync(imagePath)) {
        // Read the image file
        const imageBuffer = fs.readFileSync(imagePath);
        // Convert to base64 data URL
        const base64 = imageBuffer.toString('base64');
        return `data:image/png;base64,${base64}`;
      }
    } catch (error) {
      console.warn('Failed to load invoice shadder image:', error);
    }

    // Return empty string if image can't be loaded
    return '';
  }

  /**
   * Generate HTML table rows for invoice orders
   * @param orders - Array of order data
   * @returns HTML string with table rows
   */
  private static generateInvoiceOrdersTable(orders: any[]): string {
    if (!orders || orders.length === 0) {
      return '<tr class="border-t"><td colspan="4" class="text-center">No orders found</td></tr>';
    }

    return orders
      .map((order) => {
        const trackingNumber = order.trackingNumber || 'N/A';
        const serviceLevel = order.serviceLevel || order.priceSet || 'N/A';
        const completedDate = order.actualDeliveryTime
          ? formatDateTime(
              order.actualDeliveryTime,
              DEFAULT_DATETIME_FORMAT_12H,
            )
          : order.createdAt
            ? formatDateTime(order.createdAt, DEFAULT_DATETIME_FORMAT_12H)
            : 'N/A';
        const amount = order.totalPrice ? `$${order.totalPrice}` : '$0.00';

        return `
        <tr class="border-t">
          <td>${trackingNumber}</td>
          <td>${serviceLevel}</td>
          <td>${completedDate}</td>
          <td>${amount}</td>
        </tr>`;
      })
      .join('');
  }

  /**
   * Generate HTML table rows for invoice orders - first page only (limited rows)
   * @param orders - Array of order data
   * @param maxRows - Maximum number of rows to show on first page (default: 8)
   * @returns HTML string with table rows
   */
  private static generateInvoiceOrdersTablePageOne(
    orders: any[],
    maxRows: number = 18,
  ): string {
    if (!orders || orders.length === 0) {
      return '<tr class="border-t"><td colspan="4" class="text-center">No orders found</td></tr>';
    }

    // Take only the first maxRows orders for page one
    const pageOneOrders = orders.slice(0, maxRows);

    return pageOneOrders
      .map((order) => {
        const trackingNumber = order.trackingNumber || 'N/A';
        const serviceLevel = order.serviceLevel || order.priceSet || 'N/A';
        const completedDate = order.actualDeliveryTime
          ? formatDateTime(
              order.actualDeliveryTime,
              DEFAULT_DATETIME_FORMAT_12H,
            )
          : order.createdAt
            ? formatDateTime(order.createdAt, DEFAULT_DATETIME_FORMAT_12H)
            : 'N/A';
        const amount = order.totalPrice ? `$${order.totalPrice}` : '$0.00';

        return `
        <tr class="border-t">
          <td>${trackingNumber}</td>
          <td>${serviceLevel}</td>
          <td>${completedDate}</td>
          <td>${amount}</td>
        </tr>`;
      })
      .join('');
  }

  /**
   * Generate indicator message when there are more orders than shown on first page
   * @param orders - Array of order data
   * @param maxRowsPageOne - Maximum number of rows shown on first page (default: 8)
   * @returns HTML string with indicator message or empty string
   */
  private static generateMoreOrdersIndicator(
    orders: any[],
    maxRowsPageOne: number = 8,
  ): string {
    if (!orders || orders.length <= maxRowsPageOne) {
      return ''; // No additional orders
    }

    const remainingCount = orders.length - maxRowsPageOne;

    return `
    <div class="mt-2 text-center text-sm text-primary-300 bg-primary-25 border border-primary-50 rounded-lg p-3">
      <p class="font-medium">
        Showing ${maxRowsPageOne} of ${orders.length} orders.
        <span class="font-bold text-primary-900">${remainingCount} additional order${remainingCount > 1 ? 's' : ''} continued on next page${remainingCount > 1 ? 's' : ''}.</span>
      </p>
    </div>`;
  }

  /**
   * Generate HTML for remaining orders on subsequent pages
   * @param orders - Array of order data
   * @param maxRowsPageOne - Maximum number of rows shown on first page (default: 8)
   * @param ordersPerPage - Number of orders to show per subsequent page (default: 20)
   * @returns HTML string with complete tables for remaining orders
   */
  private static generateInvoiceOrdersRemaining(
    orders: any[],
    maxRowsPageOne: number = 18,
    ordersPerPage: number = 20,
  ): string {
    if (!orders || orders.length <= maxRowsPageOne) {
      return ''; // No remaining orders
    }

    // Get remaining orders after the first page
    const remainingOrders = orders.slice(maxRowsPageOne);

    if (remainingOrders.length === 0) {
      return '';
    }

    // Calculate number of pages needed for remaining orders
    const totalPages = Math.ceil(remainingOrders.length / ordersPerPage);
    let result = '';

    for (let pageIndex = 0; pageIndex < totalPages; pageIndex++) {
      const startIndex = pageIndex * ordersPerPage;
      const endIndex = Math.min(
        startIndex + ordersPerPage,
        remainingOrders.length,
      );
      const pageOrders = remainingOrders.slice(startIndex, endIndex);
      const currentPageNumber = pageIndex + 2; // +2 because first page is page 1

      // Add page break before each new page
      result +=
        '<div style="page-break-before: always; margin-top: 20px;"></div>';

      // Add page header with continuation indicator
      result += `
      <div class="mb-4">
        <div class="text-center text-lg font-bold text-primary-900 mb-2">
          Invoice Orders (Continued) - Page ${currentPageNumber}
        </div>
        <div class="text-center text-sm text-primary-300 mb-4">
          Showing orders ${maxRowsPageOne + startIndex + 1} to ${maxRowsPageOne + endIndex} of ${orders.length} total orders
        </div>
      </div>`;

      // Generate table for this page
      const tableRows = pageOrders
        .map((order) => {
          const trackingNumber = order.trackingNumber || 'N/A';
          const serviceLevel = order.serviceLevel || order.priceSet || 'N/A';
          const completedDate = order.actualDeliveryTime
            ? formatDateTime(
                order.actualDeliveryTime,
                DEFAULT_DATETIME_FORMAT_12H,
              )
            : order.createdAt
              ? formatDateTime(order.createdAt, DEFAULT_DATETIME_FORMAT_12H)
              : 'N/A';
          const amount = order.totalPrice ? `$${order.totalPrice}` : '$0.00';

          return `
          <tr class="border-t">
            <td>${trackingNumber}</td>
            <td>${serviceLevel}</td>
            <td>${completedDate}</td>
            <td>${amount}</td>
          </tr>`;
        })
        .join('');

      result += `
      <div class="mt-4 overflow-auto border border-primary-50 rounded-lg">
        <table class="template-table min-w-full text-sm text-left !text-primary-900">
          <thead>
            <tr class="bg-primary-25">
              <th>Tracking Number</th>
              <th>Service Level</th>
              <th>Completed Date</th>
              <th>Amount</th>
            </tr>
          </thead>
          <tbody>
            ${tableRows}
          </tbody>
        </table>
      </div>`;
    }

    return result;
  }

  private static generateInvoiceOrderDetails(orders: any[]): string {
    if (!orders || orders.length === 0) {
      return '';
    }

    // Start with a page break to ensure order details start on a new page
    let result = '<div style="page-break-before: always;"></div>';

    // Process orders in chunks of 2 per page (maximum)
    const ordersPerPage = 2;
    const totalPages = Math.ceil(orders.length / ordersPerPage);

    for (let pageIndex = 0; pageIndex < totalPages; pageIndex++) {
      const startIndex = pageIndex * ordersPerPage;
      const endIndex = Math.min(startIndex + ordersPerPage, orders.length);
      const pageOrders = orders.slice(startIndex, endIndex);

      // Add page break and margin before each new page (except the first one)
      if (pageIndex > 0) {
        result +=
          '<div style="page-break-before: always; margin-top: 20px;"></div>';
      }

      // Generate orders for this page
      const pageContent = pageOrders
        .map((order: OrderDetailResponseDto) => {
          // Add page-break-inside: avoid to prevent order from being cut across pages
          return `<div
          class="mt-6 text-primary-900 leading-[18px] rounded-lg overflow-hidden border border-[#BFC2DF] text-sm leading-[22px]"
          style="page-break-inside: avoid;"
          >
          <div class="bg-[#F5F6FF]">
            <div class="flex items-center px-4 py-2">
              <div class="flex-1">
                <span class="font-bold">Tracking Number:</span>
                ${order.trackingNumber}
              </div>
              <div class="flex-1">
                <span class="font-bold">Order Date:</span>
                ${order.createdAt ? formatDateTime(order.createdAt, DEFAULT_DATETIME_FORMAT_12H) : 'N/A'}
              </div>
            </div>
            <div class="flex gap-4 border-y border-[#E3E5F6] px-4 py-2">
              <div class="flex-1">
                <span class="font-bold mb-1 block">From:</span>
                <div>
                  <div class="flex gap-1">
                    <span class="font-bold">${order.collectionCompanyName} </span>
                    <p>(${order.collectionContactName})</p>
                  </div>
                  <p>
                    ${order.collectionAddressSummary}
                  </p>
                </div>
              </div>
              <div class="flex-1">
                <span class="font-bold mb-1 block">To:</span>
                <div>
                  <div class="flex gap-1">
                    <span class="font-bold">${order.deliveryCompanyName} </span>
                    <p>(${order.deliveryContactName})</p>
                  </div>
                  <p>
                    ${order.deliveryAddressSummary}
                  </p>
                </div>
              </div>
            </div>
            <div class="bg-white px-4 pb-2">
              <div
                class="flex gap-4 justify-between items-center border-b border-b-[#E3E5F6] py-2"
              >
                <span class="flex-1 font-bold">Order Details</span>
                <span class="flex-1 font-bold">Service Details</span>
              </div>
              <!-- Package details -->
              <div class="flex gap-4 mt-2">
                <div class="flex flex-col gap-2 flex-1">
                  <div class="flex gap-4">
                    <div class="mb-1 w-[35%]">Package Details:</div>
                    <div class="leading-[24px] w-[65%]">
                    ${this.generateOrderItems(order.items)}
                    </div>
                  </div>
                  <div class="flex gap-4 border-b border-b-[#E3E5F6]">
                    <div class="mb-1 w-[35%]">Total Quantity:</div>
                    <div class="leading-[24px] w-[65%]">${order.totalItems}</div>
                  </div>
                  <div class="flex gap-4">
                    <div class="mb-1 w-[35%]">Delivered to:</div>
                    <div class="leading-[24px] w-[65%]">${order.deliveryContactName}</div>
                  </div>
                  <div class="flex gap-4">
                    <div class="mb-1 w-[35%]">Requested by:</div>
                    <div class="leading-[24px] w-[65%]">${order.requestedByName}</div>
                  </div>
                  <div class="flex gap-4">
                    <div class="mb-1 w-[35%]">Delivery Time:</div>
                    <div class="leading-[24px] w-[65%]">${order.scheduledDeliveryTime ? formatDateTime(order.scheduledDeliveryTime, DEFAULT_DATETIME_FORMAT_12H) : 'N/A'}</div>
                  </div>
                </div>
                <!-- Service details -->
                <div class="flex flex-col gap-2 order-b-[#E3E5F6] flex-1">
                  <div class="flex gap-4">
                    <div class="mb-1 w-[35%]">Service Level:</div>
                    <div class="leading-[24px] w-[65%]">${order.serviceLevel}</div>
                  </div>
                  <div class="flex gap-4">
                    <div class="mb-1 w-[35%]">Base Price:</div>
                    <div class="leading-[24px] w-[65%]">$${order.basePrice}</div>
                  </div>
                  <div class="flex gap-4">
                    <div class="mb-1 w-[35%]">Additional Charges:</div>
                    <div class="leading-[24px] w-[65%]">
                      <p>$2.10 - Additional Fuel Fee 15%</p>
                      <p>$14.00 - Vnp Group Nextday Comprehensive Service</p>
                      <p>$10.00 - Same-Day Charges for Canada Inc.</p>
                      <p>$3.00 - Pricing Adjustment for 10-inch Height</p>
                    </div>
                  </div>
                  <div class="flex gap-4">
                    <div class="mb-1 w-[35%] font-bold">Total Price:</div>
                    <div class="leading-[24px] w-[65%] font-bold">$${order.totalPrice}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>`;
        })
        .join('');

      result += pageContent;
    }

    return result;
  }

  private static generateOrderItems(
    items: OrderItemResponseDto[] = [],
  ): string {
    if (!items || items.length === 0) {
      return 'N/A';
    }
    return items.length > 0
      ? items
          .map(
            (item: OrderItemResponseDto) =>
              `<p>${item.quantity} ${item.packageTemplateName} (${item.length}LX ${item.width}W X ${item.height}H) ${item.totalWeight || 0}kg</p>`,
          )
          .join('')
      : 'N/A';
  }

  /**
   * Escape special regex characters in a string
   */
  private static escapeRegExp(string: string): string {
    return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
  }

  /**
   * Get all available template variables
   */
  public static getAvailableVariables(): TEMPLATE_VARIABLE[] {
    return Object.values(TEMPLATE_VARIABLE);
  }

  /**
   * Validate if a template contains valid template variables
   */
  public static validateTemplate(template: string): {
    isValid: boolean;
    invalidVariables: string[];
  } {
    const templateVariablePattern = /TEMPLATE_VARIABLE\.[A-Z_]+/g;
    const foundVariables = template.match(templateVariablePattern) || [];
    const validVariables = Object.values(TEMPLATE_VARIABLE);

    const invalidVariables = foundVariables.filter(
      (variable) => !validVariables.includes(variable as TEMPLATE_VARIABLE),
    );

    return {
      isValid: invalidVariables.length === 0,
      invalidVariables,
    };
  }
}
